#!/usr/bin/env python3
"""
Test the multiprocessing fix for evaluation noise in hyperparameter tuning.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import torch as th
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔧 TESTING MULTIPROCESSING EVALUATION NOISE FIX")
print("=" * 55)

# Create simple test data
test_data = []
symbols = ['AAPL', 'MSFT', 'GOOGL']

for day in range(30):
    for symbol in symbols:
        base_price = {'AAPL': 150, 'MSFT': 300, 'GOOGL': 100}[symbol]
        price = base_price * (1 + 0.01 * day)
        
        test_data.append({
            'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
            'close': price, 'open': price, 'high': price * 1.01, 'low': price * 0.99,
            'volume': 1000000, 'day': day,
            'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
            'ema_12': price, 'ema_26': price, 'rsi_14': 50, 'cci_20': 0,
            'macd_12_26_9': 0.5, 'macds_12_26_9': -0.5, 'macdh_12_26_9': 0.1,
            'adx_14': 25, 'dmp_14': 15, 'dmn_14': 10, 'bbl_20_2.0': price * 0.95,
            'bbm_20_2.0': price, 'bbu_20_2.0': price * 1.05, 'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5,
            'obv': 1000000, 'turbulence': 0.1, 'price_range': 0.02, 'price_position': 0.5,
            'returns_1d': 0.01, 'returns_5d': 0.05, 'returns_20d': 0.2,
            'volume_ma_20': 1000000, 'volume_ratio': 1.0, 'volatility_20d': 0.02,
            'vix_ma_5': 20, 'vix_ma_20': 20, 'vix_percentile_252': 0.5,
            'vix_change': 0, 'vix_change_5d': 0, 'vix_regime_numeric': 1.0
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['day', 'tic']).reset_index(drop=True)
df = df.set_index('day')

print(f"Created test dataset: {len(df)} rows, {len(df.index.unique())} days")

tech_indicators = ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26',
                  'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
                  'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0',
                  'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position',
                  'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio',
                  'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252',
                  'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']

try:
    print("\n📊 Test 1: Environment WITHOUT evaluation noise...")
    env1 = AsymmetricTradingEnv(
        df=df, stock_dim=3, hmax=100, initial_amount=100000,
        num_stock_shares=[0, 0, 0], buy_cost_pct=[0.001, 0.001, 0.001],
        sell_cost_pct=[0.001, 0.001, 0.001], reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=symbols),
        log_level='ERROR', tech_indicator_list=tech_indicators,
        evaluation_noise_scale=0.0  # NO noise
    )
    
    print("\n📊 Test 2: Environment WITH evaluation noise...")
    env2 = AsymmetricTradingEnv(
        df=df, stock_dim=3, hmax=100, initial_amount=100000,
        num_stock_shares=[0, 0, 0], buy_cost_pct=[0.001, 0.001, 0.001],
        sell_cost_pct=[0.001, 0.001, 0.001], reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=symbols),
        log_level='ERROR', tech_indicator_list=tech_indicators,
        evaluation_noise_scale=0.05  # WITH noise
    )
    
    # Create a simple deterministic actor for testing
    class TestActor(th.nn.Module):
        def __init__(self, action_dim):
            super().__init__()
            self.dummy_param = th.nn.Parameter(th.tensor([0.0]))
            self.action_dim = action_dim
            
        def forward(self, state):
            return th.tensor([[0.1] * self.action_dim], dtype=th.float32)
    
    actor = TestActor(3)
    
    print("\n📊 Testing environment WITHOUT noise (should be deterministic)...")
    from utils.evaluation_noise import EvaluationNoiseManager
    
    # Ensure noise is disabled for first test
    EvaluationNoiseManager.disable()
    
    results1 = []
    try:
        import elegantrl.train.evaluator as evaluator_module
        for i in range(5):
            result, steps = evaluator_module.get_rewards_and_steps(env1, actor)
            results1.append(result)
            print(f"   Episode {i+1}: {result:.8f}")
        
        mean1 = np.mean(results1)
        std1 = np.std(results1)
        print(f"   Mean (avgR): {mean1:.8f}")
        print(f"   Std (stdR): {std1:.8f}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        results1 = [0.0] * 5
        std1 = 0.0
    
    print("\n📊 Testing environment WITH noise (should show variation)...")
    
    # The env2 should have already enabled noise during construction
    results2 = []
    try:
        for i in range(5):
            result, steps = evaluator_module.get_rewards_and_steps(env2, actor)
            results2.append(result)
            print(f"   Episode {i+1}: {result:.8f}")
        
        mean2 = np.mean(results2)
        std2 = np.std(results2)
        print(f"   Mean (avgR): {mean2:.8f}")
        print(f"   Std (stdR): {std2:.8f}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        results2 = [0.0] * 5
        std2 = 0.0
    
    # Check improvement
    improvement_factor = std2 / (std1 + 1e-10)
    
    print(f"\n🎯 MULTIPROCESSING FIX RESULTS:")
    print(f"   Without noise stdR: {std1:.8f}")
    print(f"   With noise stdR: {std2:.8f}")
    print(f"   Improvement factor: {improvement_factor:.2f}x")
    
    if std2 > 0.0001:
        print(f"   ✅ SUCCESS: Multiprocessing fix works! avgR will vary in tune!")
        print(f"   ✅ Worker environments can now enable evaluation noise independently")
    else:
        print(f"   ⚠️ ISSUE: Noise not properly enabled in worker process")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n🚀 The multiprocessing fix should now work for:")
print(f"   python main.py tune --trials 5")
print(f"You should see avgR varying instead of staying at 0.00!")