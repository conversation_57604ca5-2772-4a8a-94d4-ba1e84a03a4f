#!/usr/bin/env python3
"""
Debug reward calculation to understand why avgR is constant
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔍 Debugging reward calculation...")

# Create simple test data with clear price movements
test_data = []
base_prices = {'AAPL': 150, 'MSFT': 300}  # Different base prices

for i in range(10):  # 10 days
    for stock, base_price in base_prices.items():
        # Create clear upward trend for easier debugging
        price = base_price + i * 5  # $5 increase per day
        test_data.append({
            'tic': stock,
            'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': price, 'open': price*0.99, 'high': price*1.01, 'low': price*0.98, 
            'volume': 1000000,
            'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
            'ema_12': price, 'ema_26': price, 'rsi_14': 50.0,
            'macd_12_26_9': 0.0, 'macds_12_26_9': 0.0, 'macdh_12_26_9': 0.0,
            'cci_20': 0.0, 'adx_14': 30.0, 'dmp_14': 25.0, 'dmn_14': 25.0,
            'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price, 'bbu_20_2.0': price*1.05,
            'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000, 'turbulence': 0.1,
            'price_range': 0.02, 'price_position': 0.5, 'returns_1d': 0.033,  # 3.3% daily return
            'returns_5d': 0.17, 'returns_20d': 0.7, 'volume_ma_20': 1000000,
            'volume_ratio': 1.0, 'volatility_20d': 0.02, 'vix_ma_5': 20.0,
            'vix_ma_20': 20.0, 'vix_percentile_252': 0.5, 'vix_change': 0.0,
            'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📊 Test data: {len(df)} rows, {len(df.index.unique())} days")
print(f"Price progression AAPL: {df[df['tic']=='AAPL']['close'].tolist()}")
print(f"Price progression MSFT: {df[df['tic']=='MSFT']['close'].tolist()}")

try:
    # Test different reward scaling values
    for reward_scaling in [1e-4, 1e-3, 1e-2, 1e-1, 1.0]:
        print(f"\n🧪 Testing reward_scaling = {reward_scaling}")
        
        env = AsymmetricTradingEnv(
            df=df,
            stock_dim=2,
            hmax=100,
            initial_amount=100000,
            num_stock_shares=[0, 0],
            buy_cost_pct=[0.001, 0.001],
            sell_cost_pct=[0.001, 0.001],
            reward_scaling=reward_scaling,  # Test different scaling
            asymmetric_config=AsymmetricConfig(symbols=['AAPL', 'MSFT']),
            log_level='ERROR',  # Reduce noise
            tech_indicator_list=[
                'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 
                'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
                'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 
                'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 
                'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 
                'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 
                'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
            ]
        )
        
        # Run episode with strong buy actions
        state, info = env.reset()
        total_reward = 0
        rewards = []
        portfolio_values = []
        
        for step in range(8):  # Run for 8 steps
            # Strong buy action (buy both stocks)
            action = np.array([0.8, 0.8])  # Strong buy signals
            state, reward, terminated, truncated, info = env.step(action)
            
            total_reward += reward
            rewards.append(reward)
            portfolio_values.append(info.get('total_asset', 0))
            
            print(f"  Step {step}: Action={action}, Reward={reward:.8f}, Portfolio=${info.get('total_asset', 0):.2f}")
            
            if terminated or truncated:
                break
        
        print(f"  📊 Total reward: {total_reward:.8f}")
        print(f"  📊 Portfolio change: ${portfolio_values[0]:.2f} → ${portfolio_values[-1]:.2f}")
        print(f"  📊 Reward variation: {np.std(rewards):.8f}")
        
        if np.std(rewards) > 1e-6:
            print(f"  ✅ Good reward variation with scaling {reward_scaling}")
        else:
            print(f"  ❌ Poor reward variation with scaling {reward_scaling}")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n📋 Summary:")
print(f"This test checks if:")
print(f"  1. Rewards are being calculated correctly")
print(f"  2. Reward scaling affects variation")
print(f"  3. Trading actions produce meaningful portfolio changes")
print(f"  4. Reward shaping is working as expected")