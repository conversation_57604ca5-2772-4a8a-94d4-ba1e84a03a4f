#!/usr/bin/env python3
"""
Final test of the evaluation noise fix with higher noise scale.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import torch as th
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔧 FINAL TEST: EVALUATION NOISE FIX WITH HIGHER SCALE")
print("=" * 60)

# Create test data
test_data = []
symbols = ['AAPL', 'MSFT']

for day in range(20):
    for symbol in symbols:
        base_price = {'AAPL': 150, 'MSFT': 300}[symbol]
        price = base_price * (1 + 0.01 * day)
        
        test_data.append({
            'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
            'close': price, 'open': price, 'high': price * 1.01, 'low': price * 0.99,
            'volume': 1000000, 'day': day,
            'sma_5': price, 'sma_10': price, 'rsi_14': 50, 'turbulence': 0.1
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['day', 'tic']).reset_index(drop=True)
df = df.set_index('day')

print(f"Created test dataset: {len(df)} rows, {len(df.index.unique())} days")

# Create environment with HIGHER noise scale (simulating worker process)
env = AsymmetricTradingEnv(
    df=df, stock_dim=2, hmax=100, initial_amount=100000,
    num_stock_shares=[0, 0], buy_cost_pct=[0.001, 0.001],
    sell_cost_pct=[0.001, 0.001], reward_scaling=1e-4,
    asymmetric_config=AsymmetricConfig(symbols=symbols),
    log_level='ERROR', tech_indicator_list=['sma_5', 'sma_10', 'rsi_14', 'turbulence'],
    evaluation_noise_scale=0.1  # HIGHER noise scale
)

class TestActor(th.nn.Module):
    def __init__(self, action_dim):
        super().__init__()
        self.dummy_param = th.nn.Parameter(th.tensor([0.0]))
        self.action_dim = action_dim
        
    def forward(self, state):
        return th.tensor([[0.1] * self.action_dim], dtype=th.float32)

actor = TestActor(2)

print(f"\n📊 Testing with evaluation_noise_scale=0.1...")

# Test with ElegantRL evaluation
try:
    import elegantrl.train.evaluator as evaluator_module
    
    results = []
    for i in range(5):
        result, steps = evaluator_module.get_rewards_and_steps(env, actor)
        results.append(result)
        print(f"   Episode {i+1}: {result:.10f}")
    
    mean_result = np.mean(results)
    std_result = np.std(results)
    
    print(f"\n🎯 RESULTS:")
    print(f"   Mean (avgR): {mean_result:.10f}")
    print(f"   Std (stdR): {std_result:.10f}")
    print(f"   Min: {np.min(results):.10f}")
    print(f"   Max: {np.max(results):.10f}")
    print(f"   Range: {np.max(results) - np.min(results):.10f}")
    
    # Check if variation is sufficient for ElegantRL to detect
    if std_result > 1e-6:  # More reasonable threshold
        print(f"   ✅ SUCCESS: Strong variation! avgR will vary in hyperparameter tuning")
        print(f"   ✅ ElegantRL should now show avgR != 0.00 and stdR > 0.0")
    elif std_result > 1e-9:
        print(f"   ⚠️ WEAK: Some variation but may be too small for ElegantRL display")
    else:
        print(f"   ❌ ISSUE: Still deterministic")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

# Check noise manager state
from utils.evaluation_noise import EvaluationNoiseManager
manager = EvaluationNoiseManager.get_instance()
print(f"\n🔧 Noise Manager State:")
print(f"   Enabled: {manager.enabled}")
print(f"   Noise scale: {manager.noise_scale}")
print(f"   Action noise scale: {manager.action_noise_scale}")
print(f"   Return noise scale: {manager.return_noise_scale}")
print(f"   State noise scale: {manager.state_noise_scale}")

print(f"\n🚀 If this shows strong variation, then hyperparameter tuning will work!")
print(f"Run: python main.py tune --trials 3")