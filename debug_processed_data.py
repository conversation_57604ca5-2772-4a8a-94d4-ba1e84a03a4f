#!/usr/bin/env python3
"""Debug the actual processed data that main.py creates"""

import sys
sys.path.insert(0, 'src')

print("🔍 STEP 1: Recreate the exact data processing from main.py")

try:
    from data.processor import DataProcessor
    from data.fetcher import DataFetcher  
    from data.cache import DataCache
    from config.settings import Settings
    import pandas as pd
    
    # Initialize exactly like main.py
    settings = Settings()
    cache = DataCache(settings.data.cache_dir)
    fetcher = DataFetcher(cache)
    
    processor = DataProcessor(
        fetcher=fetcher,
        settings_obj=settings,
        tech_indicator_list=settings.data.tech_indicator_list,
        vix_features=settings.data.vix_features,
        include_vix=settings.data.include_vix
    )
    
    print("✅ Created processor")
    
    # Process data exactly like main.py
    processed_for_env_df = processor.process_stock_data()
    print(f"✅ Raw processed data shape: {processed_for_env_df.shape}")
    print(f"✅ Unique dates: {processed_for_env_df['date'].nunique()}")
    print(f"✅ Unique symbols: {processed_for_env_df['tic'].nunique()}")
    
    # Apply filtering exactly like main.py
    train_start = '2016-01-01'
    train_end = '2022-12-31'
    
    processed_for_env_df['date'] = pd.to_datetime(processed_for_env_df['date'])
    train_df = processed_for_env_df[
        (processed_for_env_df['date'] >= train_start) & 
        (processed_for_env_df['date'] <= train_end)
    ].reset_index(drop=True)
    
    print(f"\n🔍 STEP 2: After date filtering (2016-2022)")
    print(f"✅ Filtered shape: {train_df.shape}")
    print(f"✅ Unique dates: {train_df['date'].nunique()}")
    print(f"✅ Date range: {train_df['date'].min()} to {train_df['date'].max()}")
    print(f"✅ Records per symbol: {train_df.shape[0] // train_df['tic'].nunique()}")
    
    # Apply prepare_finrl_data transformation like main.py
    print(f"\n🔍 STEP 3: Apply FinRL data transformation")
    
    # Sort by date and tic (like main.py)
    train_df = train_df.sort_values(['date', 'tic']).reset_index(drop=True)
    print(f"✅ After sort - Shape: {train_df.shape}")
    
    # Apply factorize (like main.py)
    print(f"Before factorize: {train_df['date'].nunique()} unique dates")
    train_df['day'] = train_df['date'].factorize()[0]
    print(f"After factorize: {train_df['day'].nunique()} unique day values")
    print(f"Day range: {train_df['day'].min()} to {train_df['day'].max()}")
    
    # Set index (like main.py)
    train_df_final = train_df.set_index('day')
    print(f"After set_index: {len(train_df_final.index.unique())} unique index values")
    print(f"Index range: {train_df_final.index.min()} to {train_df_final.index.max()}")
    
    # Test accessing data like the environment does
    print(f"\n🔍 STEP 4: Test data access like AsymmetricTradingEnv")
    max_day = train_df_final.index.max()
    print(f"Max day: {max_day}")
    
    # Try accessing day 123 (where episodes terminate)
    try:
        day_123_data = train_df_final.loc[123, :]
        if isinstance(day_123_data, pd.DataFrame):
            print(f"✅ Day 123: Found {len(day_123_data)} records")
        else:
            print(f"✅ Day 123: Found 1 record (Series)")
    except Exception as e:
        print(f"❌ Day 123: Error - {e}")
    
    # Try accessing day 124 (where it might fail)
    try:
        day_124_data = train_df_final.loc[124, :]
        if isinstance(day_124_data, pd.DataFrame):
            print(f"✅ Day 124: Found {len(day_124_data)} records")
        else:
            print(f"✅ Day 124: Found 1 record (Series)")
    except Exception as e:
        print(f"❌ Day 124: Error - {e}")
        
    # Try accessing max day
    try:
        max_day_data = train_df_final.loc[max_day, :]
        if isinstance(max_day_data, pd.DataFrame):
            print(f"✅ Max day ({max_day}): Found {len(max_day_data)} records")
        else:
            print(f"✅ Max day ({max_day}): Found 1 record (Series)")
    except Exception as e:
        print(f"❌ Max day ({max_day}): Error - {e}")
    
    print(f"\n📊 SUMMARY:")
    print(f"Expected episodes: {max_day + 1} days")
    print(f"Environment terminates at: 123 days")
    print(f"Issue: {'❌ DATA PROBLEM' if max_day < 1500 else '✅ DATA LOOKS GOOD - ISSUE IS ELSEWHERE'}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()