#!/usr/bin/env python3
"""Simple debug script to check DataFrame size"""

import sys
sys.path.insert(0, 'src')

# First, let's check what the environment actually receives
print("🔍 STEP 1: Create training environment and check DataFrame")

try:
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    import pandas as pd
    
    # Read one of the actual data files being used
    import os
    cache_files = [f for f in os.listdir('data/cache') if f.endswith('.csv')]
    print(f"Found {len(cache_files)} cache files")
    
    # Try to find the processed training data
    print("\n🔍 STEP 2: Look for processed data files")
    for i, filename in enumerate(cache_files[:5]):  # Check first 5 files
        filepath = f'data/cache/{filename}'
        try:
            df = pd.read_csv(filepath)
            print(f"File {i+1}: {filename}")
            print(f"  Shape: {df.shape}")
            if 'date' in df.columns:
                print(f"  Date range: {df['date'].min()} to {df['date'].max()}")
                print(f"  Unique dates: {df['date'].nunique()}")
            if 'tic' in df.columns:
                print(f"  Unique symbols: {df['tic'].nunique()}")
                
            # Check if this looks like the training data
            if df.shape[0] > 15000:
                print(f"  ⭐ This might be our training data!")
                
                # Apply the same filtering as main.py
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    train_df = df[
                        (df['date'] >= '2016-01-01') & 
                        (df['date'] <= '2022-12-31')
                    ].reset_index(drop=True)
                    
                    print(f"  After filtering 2016-2022:")
                    print(f"    Shape: {train_df.shape}")
                    print(f"    Unique dates: {train_df['date'].nunique()}")
                    
                    # Apply factorize like main.py
                    train_df['day'] = train_df['date'].factorize()[0]
                    print(f"    Unique days after factorize: {train_df['day'].nunique()}")
                    
                break
        except Exception as e:
            print(f"File {i+1}: {filename} - Error: {e}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()