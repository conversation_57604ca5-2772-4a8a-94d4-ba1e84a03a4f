#!/usr/bin/env python3
"""
Test the determinism fix for avgR constant issue
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import torch
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔍 Testing determinism fix for avgR variation...")

# Create simple test data
test_data = []
for i in range(10):
    for stock in ['AAPL', 'MSFT']:
        price = 100 + i * 2
        test_data.append({
            'tic': stock,
            'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': price, 'open': price, 'high': price+1, 'low': price-1, 'volume': 1000000,
            'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
            'ema_12': price, 'ema_26': price, 'rsi_14': 50.0,
            'macd_12_26_9': 0.0, 'macds_12_26_9': 0.0, 'macdh_12_26_9': 0.0,
            'cci_20': 0.0, 'adx_14': 30.0, 'dmp_14': 25.0, 'dmn_14': 25.0,
            'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price, 'bbu_20_2.0': price*1.05,
            'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000, 'turbulence': 0.1,
            'price_range': 0.02, 'price_position': 0.5, 'returns_1d': 0.02,
            'returns_5d': 0.1, 'returns_20d': 0.4, 'volume_ma_20': 1000000,
            'volume_ratio': 1.0, 'volatility_20d': 0.02, 'vix_ma_5': 20.0,
            'vix_ma_20': 20.0, 'vix_percentile_252': 0.5, 'vix_change': 0.0,
            'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📊 Test data: {len(df)} rows, {len(df.index.unique())} days")

try:
    # Create environment
    env = AsymmetricTradingEnv(
        df=df,
        stock_dim=2,
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0, 0],
        buy_cost_pct=[0.001, 0.001],
        sell_cost_pct=[0.001, 0.001],
        reward_scaling=1.0,  # Use higher scaling for meaningful rewards
        asymmetric_config=AsymmetricConfig(symbols=['AAPL', 'MSFT']),
        log_level='ERROR',
        tech_indicator_list=[
            'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 
            'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
            'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 
            'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 
            'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 
            'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 
            'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
        ]
    )
    
    print(f"✅ Environment created: max_step={env.max_step}")
    
    # Create dummy deterministic actor
    class DummyActor(torch.nn.Module):
        def __init__(self, state_dim, action_dim):
            super().__init__()
            self.fc = torch.nn.Linear(state_dim, action_dim)
            
        def forward(self, state):
            # Always return the same actions (deterministic)
            return torch.tanh(self.fc(state))
    
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.shape[0]
    actor = DummyActor(state_dim, action_dim)
    
    print(f"\n🧪 Test 1: Standard ElegantRL evaluation (should be deterministic)")
    
    # Test original ElegantRL evaluation
    sys.path.append('/app/workspaces/ElegantRL')
    from elegantrl.train.evaluator import get_rewards_and_steps
    
    original_results = []
    for i in range(5):
        cumulative_return, episode_steps = get_rewards_and_steps(env, actor)
        original_results.append(cumulative_return)
        print(f"  Episode {i+1}: Return={cumulative_return:.8f}, Steps={episode_steps}")
    
    original_std = np.std(original_results)
    print(f"  Standard deviation: {original_std:.8f}")
    
    print(f"\n🧪 Test 2: Enhanced evaluation with noise (should have variation)")
    
    # Test with noise
    from models.fix_elegantrl_evaluator import get_rewards_and_steps_with_noise
    
    enhanced_results = []
    for i in range(5):
        cumulative_return, episode_steps = get_rewards_and_steps_with_noise(env, actor, noise_scale=0.02)
        enhanced_results.append(cumulative_return)
        print(f"  Episode {i+1}: Return={cumulative_return:.8f}, Steps={episode_steps}")
    
    enhanced_std = np.std(enhanced_results)
    print(f"  Standard deviation: {enhanced_std:.8f}")
    
    print(f"\n📊 Results:")
    print(f"  Original (deterministic): std = {original_std:.8f}")
    print(f"  Enhanced (with noise): std = {enhanced_std:.8f}")
    
    if enhanced_std > original_std * 10:
        print(f"  ✅ SUCCESS: Enhanced evaluation shows {enhanced_std/original_std:.1f}x more variation!")
        print(f"  This should fix the constant avgR issue in training.")
    else:
        print(f"  ❌ Insufficient improvement in variation")
        
    print(f"\n🎯 Next step: Run actual training to verify avgR variation in ElegantRL output")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()