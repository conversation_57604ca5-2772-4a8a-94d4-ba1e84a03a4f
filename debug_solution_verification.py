#!/usr/bin/env python3
"""
Solution verification script for ElegantRL evaluator constant avgR issue.

This script confirms the root cause and demonstrates the solution.
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
import torch as th

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))
sys.path.append('/app/workspaces/ElegantRL')

def create_test_data(num_days: int = 30) -> pd.DataFrame:
    """Create test data."""
    dates = pd.date_range('2023-01-01', periods=num_days, freq='D')
    
    data = []
    for i, date in enumerate(dates):
        base_price = 100 + i * 2
        price_variation = 5 * np.sin(i * 0.3)
        price = base_price + price_variation
        
        data.append({
            'date': date,
            'tic': 'TEST',
            'open': price * 0.99,
            'high': price * 1.02,
            'low': price * 0.97,
            'close': price,
            'volume': 1000000,
            'sma_5': price,
            'ema_12': price,
            'rsi_14': 50.0,
            'macd_12_26_9': 0.1,
            'adx_14': 25.0,
            'turbulence': 0.5
        })
    
    df = pd.DataFrame(data)
    df['date'] = pd.to_datetime(df['date'])
    df['day'] = df['date'].factorize()[0]
    df = df.set_index('day')
    
    return df

def test_identical_episodes_problem():
    """Demonstrate that identical episodes cause constant avgR."""
    print("🔍 DEMONSTRATING THE IDENTICAL EPISODES PROBLEM")
    print("="*60)
    
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    from elegantrl.train.evaluator import get_rewards_and_steps
    
    test_data = create_test_data(num_days=15)
    
    asymmetric_config = AsymmetricConfig(
        target_upside_downside_ratio=2.0,
        volatility_lookback=5
    )
    
    env = AsymmetricTradingEnv(
        df=test_data,
        stock_dim=1,
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0],
        buy_cost_pct=[0.001],
        sell_cost_pct=[0.001],
        reward_scaling=1e-4,
        tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
        asymmetric_config=asymmetric_config,
        log_level="ERROR"
    )
    
    env.max_step = 10
    
    # Test with DETERMINISTIC actor (simulates converged trained agent)
    class DeterministicActor(th.nn.Module):
        def __init__(self):
            super().__init__()
            self.dummy_param = th.nn.Parameter(th.tensor([0.0]))
            
        def forward(self, state):
            # Always return the same action for the same state
            # This simulates a fully converged agent
            state_sum = state.sum().item()
            action_value = 0.1 * np.sin(state_sum)  # Deterministic based on state
            return th.tensor([[action_value]], dtype=th.float32)
    
    deterministic_actor = DeterministicActor()
    
    print("Testing with DETERMINISTIC actor (simulates trained agent):")
    
    det_results = []
    for i in range(5):
        cumulative_return, steps = get_rewards_and_steps(env, deterministic_actor)
        det_results.append(cumulative_return)
        print(f"  Episode {i+1}: {cumulative_return:.10f}")
    
    det_std = np.std(det_results)
    print(f"  Deterministic results std: {det_std:.10f}")
    print(f"  {'❌ CONSTANT' if det_std < 1e-12 else '✅ VARYING'} avgR with deterministic actor")
    
    # Test with RANDOM actor (simulates untrained/exploring agent)
    class RandomActor(th.nn.Module):
        def __init__(self):
            super().__init__()
            self.dummy_param = th.nn.Parameter(th.tensor([0.0]))
            
        def forward(self, state):
            # Return random actions each time
            action_value = np.random.uniform(-0.5, 0.5)
            return th.tensor([[action_value]], dtype=th.float32)
    
    random_actor = RandomActor()
    
    print("\nTesting with RANDOM actor (simulates exploring agent):")
    
    rand_results = []
    for i in range(5):
        cumulative_return, steps = get_rewards_and_steps(env, random_actor)
        rand_results.append(cumulative_return)
        print(f"  Episode {i+1}: {cumulative_return:.10f}")
    
    rand_std = np.std(rand_results)
    print(f"  Random results std: {rand_std:.10f}")
    print(f"  {'❌ CONSTANT' if rand_std < 1e-10 else '✅ VARYING'} avgR with random actor")
    
    return det_std < 1e-12, rand_std > 1e-10

def test_solution_add_randomness():
    """Test solution: Add randomness to evaluation."""
    print("\n🔧 TESTING SOLUTION: ADD RANDOMNESS TO EVALUATION")
    print("="*60)
    
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    
    test_data = create_test_data(num_days=15)
    
    asymmetric_config = AsymmetricConfig(
        target_upside_downside_ratio=2.0,
        volatility_lookback=5
    )
    
    env = AsymmetricTradingEnv(
        df=test_data,
        stock_dim=1,
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0],
        buy_cost_pct=[0.001],
        sell_cost_pct=[0.001],
        reward_scaling=1e-4,
        tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
        asymmetric_config=asymmetric_config,
        log_level="ERROR"
    )
    
    env.max_step = 10
    
    # Modified get_rewards_and_steps with randomness
    def get_rewards_and_steps_with_randomness(env, actor, noise_scale=0.1):
        """Modified version that adds noise during evaluation."""
        max_step = env.max_step
        device = next(actor.parameters()).device
        
        state, info_dict = env.reset()
        episode_steps = 0
        cumulative_returns = 0.0
        
        for episode_steps in range(max_step):
            tensor_state = th.as_tensor(state, dtype=th.float32, device=device).unsqueeze(0)
            tensor_action = actor(tensor_state)
            
            # Add small amount of noise to break determinism during evaluation
            noise = th.normal(0, noise_scale, tensor_action.shape).to(device)
            tensor_action = tensor_action + noise
            
            action = tensor_action.detach().cpu().numpy()[0]
            state, reward, terminated, truncated, _ = env.step(action)
            cumulative_returns += reward
            
            if terminated or truncated:
                break
        
        return cumulative_returns, episode_steps + 1
    
    # Test deterministic actor with and without noise
    class DeterministicActor(th.nn.Module):
        def __init__(self):
            super().__init__()
            self.dummy_param = th.nn.Parameter(th.tensor([0.0]))
            
        def forward(self, state):
            state_sum = state.sum().item()
            action_value = 0.1 * np.sin(state_sum)
            return th.tensor([[action_value]], dtype=th.float32)
    
    actor = DeterministicActor()
    
    print("Testing deterministic actor WITHOUT evaluation noise:")
    no_noise_results = []
    for i in range(5):
        cumulative_return, steps = get_rewards_and_steps_with_randomness(env, actor, noise_scale=0.0)
        no_noise_results.append(cumulative_return)
        print(f"  Episode {i+1}: {cumulative_return:.10f}")
    
    no_noise_std = np.std(no_noise_results)
    print(f"  Without noise std: {no_noise_std:.10f}")
    
    print("\nTesting deterministic actor WITH evaluation noise:")
    with_noise_results = []
    for i in range(5):
        cumulative_return, steps = get_rewards_and_steps_with_randomness(env, actor, noise_scale=0.05)
        with_noise_results.append(cumulative_return)
        print(f"  Episode {i+1}: {cumulative_return:.10f}")
    
    with_noise_std = np.std(with_noise_results)
    print(f"  With noise std: {with_noise_std:.10f}")
    
    improvement = with_noise_std > no_noise_std * 10
    print(f"  {'✅ IMPROVED' if improvement else '❌ NO IMPROVEMENT'} variation with evaluation noise")
    
    return improvement

def test_alternative_solution_different_start_states():
    """Test alternative solution: Different starting states."""
    print("\n🔧 TESTING ALTERNATIVE: DIFFERENT STARTING STATES")
    print("="*60)
    
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    
    test_data = create_test_data(num_days=20)
    
    asymmetric_config = AsymmetricConfig(
        target_upside_downside_ratio=2.0,
        volatility_lookback=5
    )
    
    # Modified get_rewards_and_steps that starts from different days
    def get_rewards_and_steps_different_starts(env, actor, start_day=0):
        """Modified version that starts from different days."""
        max_step = min(10, len(test_data) - start_day - 1)
        device = next(actor.parameters()).device
        
        # Manually set the starting day
        env.day = start_day
        state, info_dict = env.reset()
        
        episode_steps = 0
        cumulative_returns = 0.0
        
        for episode_steps in range(max_step):
            tensor_state = th.as_tensor(state, dtype=th.float32, device=device).unsqueeze(0)
            tensor_action = actor(tensor_state)
            action = tensor_action.detach().cpu().numpy()[0]
            state, reward, terminated, truncated, _ = env.step(action)
            cumulative_returns += reward
            
            if terminated or truncated:
                break
        
        return cumulative_returns, episode_steps + 1
    
    class DeterministicActor(th.nn.Module):
        def __init__(self):
            super().__init__()
            self.dummy_param = th.nn.Parameter(th.tensor([0.0]))
            
        def forward(self, state):
            state_sum = state.sum().item()
            action_value = 0.1 * np.sin(state_sum)
            return th.tensor([[action_value]], dtype=th.float32)
    
    actor = DeterministicActor()
    
    # Create environment
    env = AsymmetricTradingEnv(
        df=test_data,
        stock_dim=1,
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0],
        buy_cost_pct=[0.001],
        sell_cost_pct=[0.001],
        reward_scaling=1e-4,
        tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
        asymmetric_config=asymmetric_config,
        log_level="ERROR"
    )
    
    print("Testing deterministic actor with DIFFERENT starting days:")
    different_start_results = []
    for i in range(5):
        start_day = i * 2  # Start from different days
        cumulative_return, steps = get_rewards_and_steps_different_starts(env, actor, start_day)
        different_start_results.append(cumulative_return)
        print(f"  Episode {i+1} (start day {start_day}): {cumulative_return:.10f}")
    
    diff_start_std = np.std(different_start_results)
    print(f"  Different start days std: {diff_start_std:.10f}")
    
    improvement = diff_start_std > 1e-12
    print(f"  {'✅ IMPROVED' if improvement else '❌ NO IMPROVEMENT'} variation with different start days")
    
    return improvement

def main():
    """Main function to demonstrate the issue and solutions."""
    print("🎯 ELEGANTRL CONSTANT AVGR ISSUE: ROOT CAUSE & SOLUTIONS")
    print("="*70)
    print("This script demonstrates why avgR is constant and provides solutions.")
    print("="*70)
    
    # Test 1: Demonstrate the problem
    print("\n1️⃣  DEMONSTRATING THE ROOT CAUSE")
    deterministic_constant, random_varies = test_identical_episodes_problem()
    
    # Test 2: Solution with evaluation noise
    print("\n2️⃣  SOLUTION 1: ADD EVALUATION NOISE")
    noise_improvement = test_solution_add_randomness()
    
    # Test 3: Solution with different starting states
    print("\n3️⃣  SOLUTION 2: DIFFERENT STARTING STATES")
    start_improvement = test_alternative_solution_different_start_states()
    
    # Final recommendations
    print("\n" + "="*70)
    print("🎯 FINAL ANALYSIS & RECOMMENDATIONS")
    print("="*70)
    
    print(f"\n🔍 ROOT CAUSE CONFIRMED:")
    print(f"   Deterministic trained agent + deterministic environment = constant avgR")
    print(f"   ✅ Deterministic actor causes constant results: {deterministic_constant}")
    print(f"   ✅ Random actor produces varying results: {random_varies}")
    
    print(f"\n🔧 SOLUTIONS TESTED:")
    print(f"   Solution 1 - Evaluation noise: {'✅ WORKS' if noise_improvement else '❌ FAILED'}")
    print(f"   Solution 2 - Different start states: {'✅ WORKS' if start_improvement else '❌ FAILED'}")
    
    print(f"\n💡 IMPLEMENTATION RECOMMENDATIONS:")
    print(f"   1. IMMEDIATE FIX: Add small noise during evaluation")
    print(f"      - Modify the evaluator to add ~0.05 noise to actions during eval")
    print(f"      - This breaks the determinism while preserving agent performance measurement")
    print(f"   ")
    print(f"   2. ALTERNATIVE: Evaluate on different time periods")
    print(f"      - Start evaluation episodes from different days in the data")
    print(f"      - This provides more realistic performance assessment")
    print(f"   ")
    print(f"   3. ROOT UNDERSTANDING:")
    print(f"      - The 'constant avgR' is actually correct behavior!")
    print(f"      - A fully trained deterministic agent SHOULD produce consistent results")
    print(f"      - The issue is that this makes it hard to see if training is progressing")
    print(f"   ")
    print(f"   4. MONITORING SOLUTION:")
    print(f"      - Track individual episode rewards, not just the average")
    print(f"      - Add logging to show reward distribution across evaluation episodes")
    print(f"      - Use different evaluation scenarios (bull/bear markets)")
    
    print(f"\n🎉 CONCLUSION:")
    if deterministic_constant and (noise_improvement or start_improvement):
        print(f"   ✅ Issue successfully identified and solutions verified!")
        print(f"   The constant avgR is due to deterministic evaluation, not broken training.")
        return True
    else:
        print(f"   ❌ Could not fully verify the issue or solutions.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)