#!/usr/bin/env python3
"""
Debug the real training scenario to understand why noise isn't working.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from pathlib import Path

print("🔍 DEBUGGING REAL TRAINING SCENARIO")
print("="*50)

# First, let's check if training data exists and matches expected format
data_dir = Path("data/processed")
print(f"1. Checking data directory: {data_dir}")

if data_dir.exists():
    csv_files = list(data_dir.glob("*.csv"))
    print(f"   Found {len(csv_files)} CSV files")
    
    # Try to load the processed data like main.py does
    try:
        from config.settings import settings
        print(f"   Loading data using settings...")
        
        # This mimics how main.py loads data
        processed_file = data_dir / "processed_data.csv"
        if processed_file.exists():
            df = pd.read_csv(processed_file)
            print(f"   Loaded processed data: {len(df)} rows")
            print(f"   Date range: {df['date'].min()} to {df['date'].max()}")
            print(f"   Symbols: {df['tic'].unique()}")
            print(f"   Days per symbol: {len(df) // len(df['tic'].unique())}")
            
            # Check if this matches the 1761 steps we see in training
            unique_dates = df['date'].nunique() 
            print(f"   Unique dates: {unique_dates}")
            
            if unique_dates == 1761:
                print(f"   ✅ This matches the 1761 steps in training!")
            else:
                print(f"   ❌ Mismatch: expected 1761, got {unique_dates}")
                
        else:
            print(f"   ❌ No processed_data.csv found")
            
    except Exception as e:
        print(f"   ❌ Error loading data: {e}")
        
else:
    print(f"   ❌ Data directory doesn't exist")

print(f"\n2. Testing environment setup like main.py...")

# Test environment creation similar to main.py
try:
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    from config.settings import settings
    
    # Create test data that matches the scale of real training
    print(f"   Creating test data with 1761 days...")
    
    test_data = []
    symbols = ['AAPL', 'MSFT', 'GOOGL']  # 3 symbols like main training
    
    for day in range(1761):  # Match real training length
        for symbol in symbols:
            price = 100 + day * 0.01 + np.random.normal(0, 0.5)  # Small growth + noise
            test_data.append({
                'tic': symbol,
                'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
                'close': price, 'open': price, 'high': price*1.01, 'low': price*0.99,
                'volume': 1000000, 'day': day,
                # Add all required technical indicators
                'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
                'ema_12': price, 'ema_26': price, 'rsi_14': 50.0,
                'macd_12_26_9': 0.0, 'macds_12_26_9': 0.0, 'macdh_12_26_9': 0.0,
                'cci_20': 0.0, 'adx_14': 30.0, 'dmp_14': 25.0, 'dmn_14': 25.0,
                'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price, 'bbu_20_2.0': price*1.05,
                'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000, 'turbulence': 0.1,
                'price_range': 0.02, 'price_position': 0.5, 'returns_1d': 0.0001,
                'returns_5d': 0.0005, 'returns_20d': 0.002, 'volume_ma_20': 1000000,
                'volume_ratio': 1.0, 'volatility_20d': 0.02, 'vix_ma_5': 20.0,
                'vix_ma_20': 20.0, 'vix_percentile_252': 0.5, 'vix_change': 0.0,
                'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
            })
    
    df = pd.DataFrame(test_data)
    df = df.sort_values(['date', 'tic']).reset_index(drop=True)
    df = df.set_index('day')
    
    print(f"   Created dataset: {len(df)} rows, {len(df.index.unique())} days, {len(df['tic'].unique())} symbols")
    
    # Create environment exactly like main.py
    env_config = {
        'df': df,
        'stock_dim': 3,
        'hmax': 100,
        'initial_amount': 100000,
        'num_stock_shares': [0, 0, 0],
        'buy_cost_pct': [0.001, 0.001, 0.001],
        'sell_cost_pct': [0.001, 0.001, 0.001],
        'reward_scaling': settings.reward_scaling,  # Use settings value
        'asymmetric_config': AsymmetricConfig(symbols=symbols),
        'log_level': 'ERROR',
        'tech_indicator_list': [
            'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26',
            'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
            'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0',
            'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position',
            'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio',
            'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252',
            'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
        ]
    }
    
    env = AsymmetricTradingEnv(**env_config)
    print(f"   Environment created: max_step={env.max_step}, reward_scaling={env.reward_scaling}")
    
    # Test evaluation noise
    print(f"\n3. Testing evaluation noise functionality...")
    
    print(f"   Before enabling noise: _evaluation_noise_scale = {getattr(env, '_evaluation_noise_scale', 'not found')}")
    
    # Enable noise like SAC agent does
    if hasattr(env, 'enable_evaluation_noise'):
        env.enable_evaluation_noise(noise_scale=0.05)
        print(f"   ✅ Enabled evaluation noise")
        print(f"   After enabling noise: _evaluation_noise_scale = {env._evaluation_noise_scale}")
    else:
        print(f"   ❌ Environment doesn't have enable_evaluation_noise method")
    
    # Run a few episodes to test
    print(f"\n4. Testing episode variations...")
    
    cumulative_returns = []
    for episode in range(5):
        state, _ = env.reset()
        action = np.array([0.5, 0.3, 0.2])  # Distribute across 3 stocks
        
        # Run full episode (1760 steps)
        for step in range(1760):
            state, reward, done, truncated, info = env.step(action)
            action = np.array([0.0, 0.0, 0.0])  # Hold after first step
            if done or truncated:
                break
        
        cumulative_return = getattr(env, 'cumulative_returns', 0)
        cumulative_returns.append(cumulative_return)
        print(f"   Episode {episode+1}: cumulative_returns = {cumulative_return:.8f}")
    
    # Check variation
    std_returns = np.std(cumulative_returns)
    print(f"\n   Results: {[f'{r:.8f}' for r in cumulative_returns]}")
    print(f"   Standard deviation: {std_returns:.8f}")
    
    if std_returns > 1e-8:
        print(f"   ✅ SUCCESS: Noise is working (std = {std_returns:.8f})")
    else:
        print(f"   ❌ FAILED: No variation despite noise (std = {std_returns:.8f})")
        
        # Debug: Check if noise calculation is working
        print(f"\n5. Deep debugging noise calculation...")
        
        # Manually test the noise calculation
        test_return = 0.001  # Example scaled return
        
        print(f"   Test return: {test_return}")
        print(f"   Noise scale: {env._evaluation_noise_scale}")
        
        if env._evaluation_noise_scale > 0:
            noise = np.random.normal(0, env._evaluation_noise_scale * abs(test_return))
            print(f"   Generated noise: {noise:.8f}")
            print(f"   Noisy return: {test_return + noise:.8f}")
        else:
            print(f"   ❌ Noise scale is 0!")

except Exception as e:
    print(f"   ❌ Error in environment test: {e}")
    import traceback
    traceback.print_exc()

print(f"\n🎯 LIKELY ISSUES:")
print(f"1. Multiprocessing environments don't inherit noise settings")
print(f"2. ElegantRL creates new environments that bypass our setup") 
print(f"3. Evaluation happens in worker processes without noise")
print(f"4. Configuration isn't properly passed to evaluation environments")

print(f"\n💡 SOLUTIONS TO TRY:")
print(f"1. Check if env_args includes evaluation noise settings")
print(f"2. Ensure noise is enabled in environment constructor") 
print(f"3. Add noise to environment creation parameters")
print(f"4. Debug multiprocessing environment creation")