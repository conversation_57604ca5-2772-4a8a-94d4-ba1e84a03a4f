#!/usr/bin/env python3
"""Debug what DataFrame ElegantRL actually gets during training"""

import sys
sys.path.insert(0, 'src')

print("🔍 Testing ElegantRL's environment usage")

try:
    # Import ElegantRL components
    from elegantrl.agents import AgentSAC
    from elegantrl.train.config import Config
    from elegantrl.train.evaluator import get_rewards_and_steps
    
    # Load the correct data
    import pandas as pd
    df = pd.read_csv('data/processed/processed_data.csv')
    df['date'] = pd.to_datetime(df['date'])
    train_df = df[
        (df['date'] >= '2016-01-01') & 
        (df['date'] <= '2022-12-31')
    ].reset_index(drop=True)
    
    train_df = train_df.sort_values(['date', 'tic']).reset_index(drop=True)
    train_df['day'] = train_df['date'].factorize()[0]
    train_df_final = train_df.set_index('day')
    
    print(f"✅ Prepared correct data: {len(train_df_final.index.unique())} days")
    
    # Create environment exactly like main.py
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    
    asymmetric_config = AsymmetricConfig(target_upside_downside_ratio=2.0)
    
    env_config = {
        'df': train_df_final,
        'stock_dim': 10,
        'hmax': 100,
        'initial_amount': 100000,
        'num_stock_shares': [0] * 10,
        'buy_cost_pct': [0.001] * 10,
        'sell_cost_pct': [0.001] * 10,
        'reward_scaling': 0.0001,
        'asymmetric_config': asymmetric_config,
        'log_level': 'ERROR',  # Reduce noise
        'day': 0,
        'initial': True,
        'previous_state': None,
        'model_name': 'test',
        'mode': 'train',
        'iteration': 'test'
    }
    
    env = AsymmetricTradingEnv(**env_config)
    print(f"✅ Environment created: {len(env.df.index.unique())} days")
    
    # Create a dummy actor for testing
    import torch
    class DummyActor(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.dummy = torch.nn.Parameter(torch.tensor([0.0]))
            
        def forward(self, state):
            batch_size = state.shape[0] if len(state.shape) > 1 else 1
            return torch.zeros((batch_size, 10))  # 10 actions for 10 stocks
    
    actor = DummyActor()
    
    print(f"\n🧪 Testing ElegantRL's get_rewards_and_steps function:")
    
    # This is the exact function ElegantRL uses for evaluation
    try:
        cumulative_returns, episode_steps = get_rewards_and_steps(env, actor)
        print(f"✅ Episode completed!")
        print(f"   Steps: {episode_steps}")
        print(f"   Cumulative returns: {cumulative_returns}")
        
        if episode_steps <= 125:
            print(f"❌ FOUND THE BUG! Episode only ran {episode_steps} steps")
            print(f"   This matches the 123-step termination we see in training!")
        else:
            print(f"✅ Episode ran full length ({episode_steps} steps)")
            
    except Exception as e:
        print(f"❌ Error in get_rewards_and_steps: {e}")
        import traceback
        traceback.print_exc()
    
    # Test multiple episodes to see if it's consistent
    print(f"\n🔄 Testing multiple episodes:")
    for i in range(3):
        try:
            cumulative_returns, episode_steps = get_rewards_and_steps(env, actor)
            print(f"Episode {i+1}: {episode_steps} steps, returns: {cumulative_returns:.8f}")
        except Exception as e:
            print(f"Episode {i+1}: Error - {e}")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()