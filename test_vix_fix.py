#!/usr/bin/env python3
"""
Test script to verify that the VIX regime fix is working correctly.
"""

import sys
sys.path.append('.')

def test_vix_filtering_logic():
    """Test the VIX filtering logic to ensure text-based features are excluded."""
    print("Testing VIX filtering logic...")
    
    # Simulate the data that would be processed
    added_vix_feature_names = ['vix_ma_5', 'vix_ma_20', 'vix_regime', 'vix_regime_numeric', 'vix_change']
    text_based_vix_features = ['vix_regime']
    
    # Apply the filtering logic from the fix
    numeric_vix_features = [f_name for f_name in added_vix_feature_names if f_name not in text_based_vix_features]
    
    print(f"All VIX features: {added_vix_feature_names}")
    print(f"Text-based features to exclude: {text_based_vix_features}")
    print(f"Numeric features that should be added: {numeric_vix_features}")
    
    # Check if vix_regime is correctly excluded
    if 'vix_regime' not in numeric_vix_features:
        print("✅ SUCCESS: vix_regime correctly excluded from numeric features")
    else:
        print("❌ FAILURE: vix_regime was not excluded from numeric features")
        return False
    
    # Check if vix_regime_numeric is included
    if 'vix_regime_numeric' in numeric_vix_features:
        print("✅ SUCCESS: vix_regime_numeric correctly included in numeric features")
    else:
        print("❌ FAILURE: vix_regime_numeric was not included in numeric features")
        return False
    
    return True

def test_tech_indicator_list_simulation():
    """Simulate the tech indicator list extension logic."""
    print("\nTesting tech indicator list extension logic...")
    
    # Simulate existing tech indicator list
    tech_indicator_list = ['sma_5', 'sma_10', 'rsi_14', 'vix_regime_numeric']
    
    # Simulate new VIX features being added
    added_vix_feature_names = ['vix_ma_5', 'vix_ma_20', 'vix_regime', 'vix_regime_numeric', 'vix_change']
    text_based_vix_features = ['vix_regime']
    
    # Apply the filtering and extension logic
    numeric_vix_features = [f_name for f_name in added_vix_feature_names if f_name not in text_based_vix_features]
    newly_added_vix_for_list = [f_name for f_name in numeric_vix_features if f_name not in tech_indicator_list]
    
    print(f"Existing tech_indicator_list: {tech_indicator_list}")
    print(f"Numeric VIX features: {numeric_vix_features}")
    print(f"Features to be added: {newly_added_vix_for_list}")
    
    # Check results
    expected_new_features = ['vix_ma_5', 'vix_ma_20', 'vix_change']
    if set(newly_added_vix_for_list) == set(expected_new_features):
        print("✅ SUCCESS: Correct features identified for addition")
    else:
        print(f"❌ FAILURE: Expected {expected_new_features}, got {newly_added_vix_for_list}")
        return False
    
    # Check that vix_regime is not in the list to be added
    if 'vix_regime' not in newly_added_vix_for_list:
        print("✅ SUCCESS: vix_regime correctly excluded from features to be added")
    else:
        print("❌ FAILURE: vix_regime was included in features to be added")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 Testing VIX regime filtering fix...")
    
    success1 = test_vix_filtering_logic()
    success2 = test_tech_indicator_list_simulation()
    
    if success1 and success2:
        print("\n🎉 All tests passed! The VIX filtering logic should work correctly.")
    else:
        print("\n💥 Some tests failed! There may be an issue with the logic.")
