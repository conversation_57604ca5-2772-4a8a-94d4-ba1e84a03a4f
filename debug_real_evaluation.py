#!/usr/bin/env python3
"""
Debug the REAL evaluation function being used by ElegantRL during hyperparameter tuning.
"""

import sys
sys.path.insert(0, 'src')

print("🔧 DEBUGGING REAL ELEGANTRL EVALUATION FUNCTION")
print("=" * 55)

# Check the actual function being used
print("✅ Test 1: Check actual ElegantRL evaluation function")
try:
    import elegantrl.train.evaluator as evaluator_module
    
    print(f"   Module path: {evaluator_module.__file__}")
    print(f"   Function: {evaluator_module.get_rewards_and_steps}")
    print(f"   Function name: {evaluator_module.get_rewards_and_steps.__name__}")
    print(f"   Function module: {getattr(evaluator_module.get_rewards_and_steps, '__module__', 'None')}")
    
    # Check if it's been monkey-patched
    if hasattr(evaluator_module.get_rewards_and_steps, '__self__'):
        print(f"   Is bound method: Yes, bound to {evaluator_module.get_rewards_and_steps.__self__}")
    else:
        print(f"   Is bound method: No")
        
except Exception as e:
    print(f"   ❌ Error: {e}")
    exit(1)

# Test 2: Enable our noise manager and check if it affects this function
print("\n✅ Test 2: Enable noise manager and test the actual function")
from utils.evaluation_noise import EvaluationNoiseManager

# Enable noise manager
EvaluationNoiseManager.enable(noise_scale=0.1)
print(f"   Noise manager enabled: {EvaluationNoiseManager.is_enabled()}")
print(f"   Function after enable: {evaluator_module.get_rewards_and_steps}")
print(f"   Function name after enable: {evaluator_module.get_rewards_and_steps.__name__}")

# Test 3: Create a minimal environment and test the actual function
print("\n✅ Test 3: Test the ACTUAL evaluation function with noise")
import numpy as np
import pandas as pd
import torch as th
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

# Create minimal test data
test_data = []
for day in range(10):
    for symbol in ['AAPL']:
        test_data.append({
            'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
            'close': 150, 'open': 150, 'high': 151, 'low': 149,
            'volume': 1000000, 'day': day,
            'sma_5': 150, 'rsi_14': 50, 'turbulence': 0.1
        })

df = pd.DataFrame(test_data).set_index('day')

# Create environment
env = AsymmetricTradingEnv(
    df=df, stock_dim=1, hmax=100, initial_amount=100000,
    num_stock_shares=[0], buy_cost_pct=[0.001], sell_cost_pct=[0.001],
    asymmetric_config=AsymmetricConfig(symbols=['AAPL']),
    log_level='ERROR', tech_indicator_list=['sma_5', 'rsi_14', 'turbulence'],
    evaluation_noise_scale=0.1  # Enable noise
)

class TestActor(th.nn.Module):
    def __init__(self):
        super().__init__()
        self.dummy = th.nn.Parameter(th.tensor([0.0]))
    def forward(self, state):
        return th.tensor([[0.1]], dtype=th.float32)

actor = TestActor()

print(f"   Testing the ACTUAL get_rewards_and_steps function...")

# Test the actual function multiple times
results = []
for i in range(5):
    result, steps = evaluator_module.get_rewards_and_steps(env, actor)
    results.append(result)
    print(f"   Call {i+1}: {result:.10f}")

mean_result = np.mean(results)
std_result = np.std(results)

print(f"\n🎯 ACTUAL FUNCTION RESULTS:")
print(f"   Mean: {mean_result:.10f}")
print(f"   Std:  {std_result:.10f}")
print(f"   Min:  {np.min(results):.10f}")
print(f"   Max:  {np.max(results):.10f}")

if std_result > 1e-6:
    print(f"   ✅ SUCCESS: The ACTUAL function shows variation!")
else:
    print(f"   ❌ PROBLEM: The ACTUAL function is still deterministic")
    
    # Debug: Check if our monkey-patch is actually being applied
    print(f"\n🔍 DEBUG: Check monkey-patch status")
    manager = EvaluationNoiseManager.get_instance()
    print(f"   Manager enabled: {manager.enabled}")
    print(f"   Manager applied: {manager._applied}")
    print(f"   Original functions stored: {list(manager.original_functions.keys())}")
    
    # Check current function properties
    current_func = evaluator_module.get_rewards_and_steps
    print(f"   Current function type: {type(current_func)}")
    print(f"   Current function name: {current_func.__name__}")
    
    if hasattr(current_func, '__wrapped__'):
        print(f"   Function is wrapped: {current_func.__wrapped__}")
    
    # Test the monkey-patch directly
    print(f"\n🔍 DEBUG: Test direct monkey-patch call")
    try:
        direct_result1 = manager._enhanced_evaluation(env, actor)
        direct_result2 = manager._enhanced_evaluation(env, actor)
        print(f"   Direct call 1: {direct_result1}")
        print(f"   Direct call 2: {direct_result2}")
        if abs(direct_result1[0] - direct_result2[0]) > 1e-10:
            print(f"   ✅ Direct calls show variation!")
        else:
            print(f"   ❌ Even direct calls are deterministic")
    except Exception as e:
        print(f"   ❌ Direct call error: {e}")