#!/usr/bin/env python3
"""
Test ElegantRL evaluation with fixed cumulative_returns attribute.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import torch
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

# Add ElegantRL to path
sys.path.append('/app/workspaces/ElegantRL')
from elegantrl.train.evaluator import get_rewards_and_steps

print("🔍 Testing ElegantRL evaluation with cumulative_returns fix")
print("="*60)

# Create test data with controlled returns
test_data = []
for i in range(20):  # 20 trading days
    price = 100 + i * 0.5  # Gradual price increase
    test_data.append({
        'tic': 'TEST', 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
        'close': price, 'open': price, 'high': price*1.01, 'low': price*0.99,
        'volume': 1000000, 'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
        'ema_12': price, 'ema_26': price, 'rsi_14': 50.0, 'macd_12_26_9': 0.0, 
        'macds_12_26_9': 0.0, 'macdh_12_26_9': 0.0, 'cci_20': 0.0, 'adx_14': 30.0, 
        'dmp_14': 25.0, 'dmn_14': 25.0, 'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price, 
        'bbu_20_2.0': price*1.05, 'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000, 
        'turbulence': 0.1, 'price_range': 0.02, 'price_position': 0.5, 'returns_1d': 0.005,
        'returns_5d': 0.025, 'returns_20d': 0.1, 'volume_ma_20': 1000000, 'volume_ratio': 1.0, 
        'volatility_20d': 0.02, 'vix_ma_5': 20.0, 'vix_ma_20': 20.0, 'vix_percentile_252': 0.5, 
        'vix_change': 0.0, 'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
    })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

# Create environment
env = AsymmetricTradingEnv(
    df=df, stock_dim=1, hmax=100, initial_amount=100000,
    num_stock_shares=[0], buy_cost_pct=[0.0], sell_cost_pct=[0.0],
    reward_scaling=1.0, asymmetric_config=AsymmetricConfig(symbols=['TEST']),
    log_level='ERROR',
    tech_indicator_list=[
        'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 
        'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
        'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 
        'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 
        'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 
        'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 
        'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
    ]
)

# Create dummy actor
class DummyActor(torch.nn.Module):
    def __init__(self, state_dim, action_dim):
        super().__init__()
        self.fc = torch.nn.Linear(state_dim, action_dim)
        
    def forward(self, state):
        # Simple buy-and-hold strategy
        return torch.tensor([[1.0]], dtype=torch.float32)

state_dim = env.observation_space.shape[0]
action_dim = env.action_space.shape[0]
actor = DummyActor(state_dim, action_dim)

print(f"Environment: {len(df)} rows, max_step: {env.max_step}")
print(f"Running ElegantRL evaluation...")

# Test multiple episodes
results = []
for episode in range(3):
    cumulative_return, episode_steps = get_rewards_and_steps(env, actor)
    results.append(cumulative_return)
    
    # Check if environment has cumulative_returns attribute
    has_cumulative_returns = hasattr(env, 'cumulative_returns')
    env_cumulative_returns = getattr(env, 'cumulative_returns', None)
    
    print(f"Episode {episode + 1}:")
    print(f"  ElegantRL return: {cumulative_return:.8f}")
    print(f"  Episode steps: {episode_steps}")
    print(f"  Has cumulative_returns: {has_cumulative_returns}")
    if has_cumulative_returns:
        print(f"  Env cumulative_returns: {env_cumulative_returns:.8f}")
        print(f"  Match: {abs(cumulative_return - env_cumulative_returns) < 1e-6}")
    
    # Check final portfolio value
    if hasattr(env, 'asset_memory') and env.asset_memory:
        final_portfolio = env.asset_memory[-1]
        initial_portfolio = env.initial_amount
        manual_return = (final_portfolio - initial_portfolio) / initial_portfolio
        print(f"  Manual calculation: {manual_return:.8f}")
        print(f"  Portfolio: ${initial_portfolio:.0f} → ${final_portfolio:.2f}")
    print()

# Analyze results
std_results = np.std(results)
mean_results = np.mean(results)

print(f"📊 EVALUATION RESULTS:")
print(f"  Episodes: {len(results)}")
print(f"  Returns: {[f'{r:.6f}' for r in results]}")
print(f"  Mean: {mean_results:.8f}")
print(f"  Std: {std_results:.8f}")

print(f"\n✅ SUCCESS INDICATORS:")
print(f"  - Environment sets cumulative_returns: {hasattr(env, 'cumulative_returns')}")
print(f"  - ElegantRL uses cumulative_returns: {std_results < 1e-10}")  # Should be deterministic
print(f"  - Returns are meaningful (>0): {mean_results > 0}")

if std_results < 1e-10:
    print(f"\n🎯 PERFECT! ElegantRL now uses cumulative_returns instead of summing step rewards")
    print(f"   This fixes the mathematical error in avgR calculation")
else:
    print(f"\n⚠️  Results still vary - might need to check evaluation noise")