"""Hyperparameter optimization using Optuna for FinRL trading models.

This module provides:
- Automated hyperparameter tuning with Optuna
- Multi-objective optimization support
- Pruning strategies for efficient search
- Custom objective functions
- Optimization result analysis
- Best parameter extraction and model training
"""

import optuna
from optuna.pruners import <PERSON>n<PERSON><PERSON><PERSON>, SuccessiveHalvingPruner
from optuna.samplers import TPESampler, CmaEsSampler
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Callable, Union
from pathlib import Path
import json
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass, asdict, field

from config import settings
from utils import get_logger, log_performance, log_error, LoggingContext
from models.sac_agent import SACAgent
from models.training import TrainingConfig


@dataclass
class OptimizationConfig:
    """Optimization configuration parameters."""
    n_trials: int = 100
    timeout: Optional[int] = None  # seconds
    n_jobs: int = 1
    sampler: str = 'tpe'  # 'tpe', 'cmaes', 'random'
    pruner: str = 'median'  # 'median', 'successive_halving', 'none'
    direction: str = 'maximize'  # 'maximize', 'minimize'
    study_name: Optional[str] = None
    storage: Optional[str] = None  # Database URL for distributed optimization
    load_if_exists: bool = True
    
    # Multi-objective settings
    directions: Optional[List[str]] = None  # For multi-objective optimization
    
    # Pruning settings
    pruning_warmup_steps: int = 5
    pruning_interval_steps: int = 1
    
    # Early stopping
    early_stopping_rounds: Optional[int] = None
    early_stopping_threshold: Optional[float] = None
    
    # SAC Hyperparameter Search Spaces
    learning_rate_range: tuple = (1e-5, 1e-2)
    batch_size_choices: List[int] = field(default_factory=lambda: [64, 128, 256, 512])
    gamma_range: tuple = (0.9, 0.999)
    tau_range: tuple = (0.001, 0.01)
    alpha_range: tuple = (0.1, 0.5)

    
    # Network architecture search
    hidden_sizes_choices: List[List[int]] = field(default_factory=lambda: [[128, 128], [256, 256], [512, 256], [256, 256, 128]])


class ObjectiveFunction:
    """Base class for optimization objective functions."""
    
    def __init__(
        self,
        train_env,
        eval_env,
        state_dim: int,
        action_dim: int,
        training_config: Optional[TrainingConfig] = None
    ):
        """Initialize objective function.
        
        Args:
            train_env: Training environment
            eval_env: Evaluation environment
            state_dim: State space dimension
            action_dim: Action space dimension
            training_config: Training configuration
        """
        self.train_env = train_env
        self.eval_env = eval_env
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.training_config = training_config or TrainingConfig()
        self.logger = get_logger(__name__)
        
        # Reduce training time for optimization (use 10k steps for faster tuning)
        optimization_timesteps = 10000  # Faster tuning with 10k steps per trial
        self.training_config.total_timesteps = optimization_timesteps
        # Set evaluation frequency for optimization (evaluate every 2k steps)
        self.training_config.eval_freq = 2000  # Evaluate more frequently during tuning
    
    def __call__(self, trial: optuna.Trial) -> Union[float, List[float]]:
        """Objective function for optimization.
        
        Args:
            trial: Optuna trial object
            
        Returns:
            Objective value(s) to optimize
        """
        raise NotImplementedError
    
    def suggest_hyperparameters(self, trial: optuna.Trial) -> Dict[str, Any]:
        """Suggest hyperparameters for the trial.
        
        Args:
            trial: Optuna trial object
            
        Returns:
            Dictionary of suggested hyperparameters
        """
        # SAC hyperparameters from config
        optuna_config = settings.optuna
        
        params = {
            'learning_rate': trial.suggest_float(
                'learning_rate',
                optuna_config.learning_rate_range[0],
                optuna_config.learning_rate_range[1],
                log=True
            ),
            'batch_size': trial.suggest_categorical(
                'batch_size',
                optuna_config.batch_size_choices
            ),
            'net_dims': trial.suggest_categorical(
                'net_dims',
                optuna_config.hidden_sizes_choices
            ),
            'gamma': trial.suggest_float(
                'gamma',
                optuna_config.gamma_range[0],
                optuna_config.gamma_range[1]
            ),
            'soft_update_tau': trial.suggest_float(
                'soft_update_tau',
                optuna_config.tau_range[0],
                optuna_config.tau_range[1],
                log=True
            ),
            'alpha': trial.suggest_float(
                'alpha',
                optuna_config.alpha_range[0],
                optuna_config.alpha_range[1]
            )
        }
        
        return params


class SingleObjective(ObjectiveFunction):
    """Single-objective optimization for return maximization."""
    
    def __call__(self, trial: optuna.Trial) -> float:
        """Optimize for maximum evaluation return.
        
        Args:
            trial: Optuna trial object
            
        Returns:
            Mean evaluation return
        """
        try:
            # Get hyperparameters
            params = self.suggest_hyperparameters(trial)
            
            # Create and train agent
            agent = SACAgent(
                state_dim=self.state_dim,
                action_dim=self.action_dim,
                config=params
            )
            
            # Train agent directly using SACAgent.train() method
            # This matches the approach used in main.py train command
            result = agent.train(
                env=self.train_env,
                total_timesteps=self.training_config.total_timesteps,
                eval_env=self.eval_env,
                eval_freq=self.training_config.eval_freq,
                save_freq=self.training_config.save_freq,
                model_dir=self.training_config.checkpoint_dir
            )
            
            if not result['success']:
                raise optuna.TrialPruned()
            
            # Final evaluation
            eval_result = agent.evaluate_policy(self.eval_env, n_episodes=5)
            return eval_result['mean_return']
            
        except optuna.TrialPruned:
            raise
        except Exception as e:
            self.logger.error(f"Trial failed: {e}")
            raise optuna.TrialPruned()


class MultiObjective(ObjectiveFunction):
    """Multi-objective optimization for return and risk."""
    
    def __call__(self, trial: optuna.Trial) -> List[float]:
        """Optimize for return and Sharpe ratio.
        
        Args:
            trial: Optuna trial object
            
        Returns:
            List of [mean_return, sharpe_ratio]
        """
        try:
            # Get hyperparameters
            params = self.suggest_hyperparameters(trial)
            
            # Create and train agent
            agent = SACAgent(
                state_dim=self.state_dim,
                action_dim=self.action_dim,
                config=params
            )
            
            # Train agent directly using SACAgent.train() method
            # This matches the approach used in main.py train command
            result = agent.train(
                env=self.train_env,
                total_timesteps=self.training_config.total_timesteps,
                eval_env=self.eval_env,
                eval_freq=self.training_config.eval_freq,
                save_freq=self.training_config.save_freq,
                model_dir=self.training_config.checkpoint_dir
            )
            
            if not result['success']:
                raise optuna.TrialPruned()
            
            # Evaluate multiple episodes for statistics
            eval_result = agent.evaluate_policy(self.eval_env, n_episodes=10)
            
            # Calculate Sharpe ratio (assuming daily returns)
            returns = []
            for _ in range(10):
                # FIX: Handle environment reset properly - it returns (state, info)
                reset_result = self.eval_env.reset()
                if isinstance(reset_result, tuple):
                    state, _ = reset_result  # Unpack (state, info)
                else:
                    state = reset_result  # Direct state return (fallback)

                episode_return = 0
                done = False

                while not done:
                    action, _ = agent.predict(state, deterministic=True)
                    step_result = self.eval_env.step(action)

                    # FIX: Handle step result properly - it might return 4 or 5 values
                    if len(step_result) == 5:
                        state, reward, done, truncated, _ = step_result
                        # For compatibility, treat truncated as done
                        done = done or truncated
                    elif len(step_result) == 4:
                        state, reward, done, _ = step_result
                    else:
                        # Fallback for unexpected return format
                        state, reward, done = step_result[0], step_result[1], step_result[2]

                    episode_return += reward

                returns.append(episode_return)
            
            returns = np.array(returns)
            sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-8)
            
            return [eval_result['mean_return'], sharpe_ratio]
            
        except optuna.TrialPruned:
            raise
        except Exception as e:
            self.logger.error(f"Trial failed: {e}")
            raise optuna.TrialPruned()


class HyperparameterOptimizer:
    """Main hyperparameter optimization orchestrator."""
    
    def __init__(self, config: Optional[OptimizationConfig] = None):
        """Initialize hyperparameter optimizer.
        
        Args:
            config: Optimization configuration
        """
        self.config = config or OptimizationConfig()
        self.logger = get_logger(__name__)
        self.study = None
        self.best_params = None
        
    def _create_study(self) -> optuna.Study:
        """Create Optuna study with configured sampler and pruner.
        
        Returns:
            Configured Optuna study
        """
        # Configure sampler
        if self.config.sampler == 'tpe':
            sampler = TPESampler()
        elif self.config.sampler == 'cmaes':
            sampler = CmaEsSampler()
        else:
            sampler = optuna.samplers.RandomSampler()
        
        # Configure pruner
        if self.config.pruner == 'median':
            pruner = MedianPruner(
                n_startup_trials=self.config.pruning_warmup_steps,
                n_warmup_steps=self.config.pruning_warmup_steps,
                interval_steps=self.config.pruning_interval_steps
            )
        elif self.config.pruner == 'successive_halving':
            pruner = SuccessiveHalvingPruner()
        else:
            pruner = optuna.pruners.NopPruner()
        
        # Create study
        if self.config.directions:  # Multi-objective
            study = optuna.create_study(
                directions=self.config.directions,
                sampler=sampler,
                pruner=pruner,
                study_name=self.config.study_name,
                storage=self.config.storage,
                load_if_exists=True # Reuse existing study
            )
        else:  # Single-objective
            study = optuna.create_study(
                direction=self.config.direction,
                sampler=sampler,
                pruner=pruner,
                study_name=self.config.study_name,
                storage=self.config.storage,
                load_if_exists=True # Reuse existing study
            )
        
        return study
    
    @log_performance
    def optimize(
        self,
        objective: ObjectiveFunction,
        save_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """Run hyperparameter optimization.
        
        Args:
            objective: Objective function to optimize
            save_dir: Directory to save optimization results
            
        Returns:
            Optimization results dictionary
        """
        save_dir = Path(save_dir or settings.model.save_dir) / "optimization"
        save_dir.mkdir(parents=True, exist_ok=True)
        
        with LoggingContext("Hyperparameter Optimization"):
            self.logger.info(f"Starting optimization with config: {asdict(self.config)}")
            
            try:
                # Create study
                self.study = self._create_study()
                
                # Add early stopping callback if configured
                callbacks = []
                if self.config.early_stopping_rounds:
                    callbacks.append(
                        self._create_early_stopping_callback()
                    )
                
                # Run optimization
                self.study.optimize(
                    objective,
                    n_trials=self.config.n_trials,
                    timeout=self.config.timeout,
                    n_jobs=self.config.n_jobs,
                    callbacks=callbacks
                )
                
                # Check if any trials completed successfully
                completed_trials = [t for t in self.study.trials if t.state == optuna.trial.TrialState.COMPLETE]
                if not completed_trials:
                    raise ValueError(f"No trials completed successfully out of {len(self.study.trials)} trials. All trials were pruned or failed.")
                
                # Extract results
                if self.config.directions:  # Multi-objective
                    best_trials = self.study.best_trials
                    self.best_params = [trial.params for trial in best_trials]
                    best_values = [trial.values for trial in best_trials]
                else:  # Single-objective
                    self.best_params = self.study.best_params
                    best_values = self.study.best_value
                
                # Save results
                self._save_optimization_results(save_dir)
                
                # Generate plots
                self._plot_optimization_results(save_dir)
                
                self.logger.success(
                    f"Optimization completed: {len(self.study.trials)} trials, "
                    f"best value: {best_values}"
                )
                
                return {
                    'success': True,
                    'n_trials': len(self.study.trials),
                    'best_params': self.best_params,
                    'best_values': best_values,
                    'study': self.study,
                    'save_dir': str(save_dir)
                }
                
            except Exception as e:
                log_error("Optimization failed", e)
                return {
                    'success': False,
                    'error': str(e)
                }
    
    def _create_early_stopping_callback(self) -> Callable:
        """Create early stopping callback.
        
        Returns:
            Early stopping callback function
        """
        best_value = None
        patience_counter = 0
        
        def callback(study, trial):
            nonlocal best_value, patience_counter
            
            if self.config.directions:  # Multi-objective
                # Use first objective for early stopping
                current_value = trial.values[0] if trial.values else None
            else:
                current_value = trial.value
            
            if current_value is None:
                return
            
            if best_value is None:
                best_value = current_value
            elif (
                (self.config.direction == 'maximize' and current_value > best_value + (self.config.early_stopping_threshold or 0)) or
                (self.config.direction == 'minimize' and current_value < best_value - (self.config.early_stopping_threshold or 0))
            ):
                best_value = current_value
                patience_counter = 0
            else:
                patience_counter += 1
            
            if patience_counter >= self.config.early_stopping_rounds:
                study.stop()
                self.logger.info(f"Early stopping triggered after {patience_counter} trials")
        
        return callback
    
    def _save_optimization_results(self, save_dir: Path) -> None:
        """Save optimization results to files.
        
        Args:
            save_dir: Directory to save results
        """
        # Save study
        study_path = save_dir / "study.pkl"
        with open(study_path, 'wb') as f:
            import pickle
            pickle.dump(self.study, f)
        
        # Save best parameters
        params_path = save_dir / "best_params.json"
        with open(params_path, 'w') as f:
            json.dump(self.best_params, f, indent=2)
        
        # Save trials dataframe
        df = self.study.trials_dataframe()
        df.to_csv(save_dir / "trials.csv", index=False)
        
        # Save optimization summary
        summary = {
            'config': asdict(self.config),
            'n_trials': len(self.study.trials),
            'best_params': self.best_params,
            'optimization_time': datetime.now().isoformat()
        }
        
        if self.config.directions:  # Multi-objective
            summary['best_values'] = [trial.values for trial in self.study.best_trials]
        else:
            summary['best_value'] = self.study.best_value
        
        with open(save_dir / "summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        self.logger.info(f"Optimization results saved to {save_dir}")
    
    def _plot_optimization_results(self, save_dir: Path) -> None:
        """Generate optimization result plots.
        
        Args:
            save_dir: Directory to save plots
        """
        try:
            # Optimization history
            fig = optuna.visualization.plot_optimization_history(self.study)
            fig.write_html(save_dir / "optimization_history.html")
            
            # Parameter importance
            if len(self.study.trials) > 10:
                fig = optuna.visualization.plot_param_importances(self.study)
                fig.write_html(save_dir / "param_importances.html")
            
            # Parallel coordinate plot
            if len(self.study.trials) > 5:
                fig = optuna.visualization.plot_parallel_coordinate(self.study)
                fig.write_html(save_dir / "parallel_coordinate.html")
            
            # Slice plot
            fig = optuna.visualization.plot_slice(self.study)
            fig.write_html(save_dir / "slice_plot.html")
            
            # Contour plot (for 2D parameter spaces)
            if len(self.study.best_params) >= 2:
                param_names = list(self.study.best_params.keys())[:2]
                fig = optuna.visualization.plot_contour(self.study, params=param_names)
                fig.write_html(save_dir / "contour_plot.html")
            
            self.logger.info(f"Optimization plots saved to {save_dir}")
            
        except Exception as e:
            self.logger.warning(f"Failed to generate some plots: {e}")
    
    def get_best_agent(
        self,
        state_dim: int,
        action_dim: int,
        multi_objective_index: int = 0
    ) -> SACAgent:
        """Create agent with best hyperparameters.
        
        Args:
            state_dim: State space dimension
            action_dim: Action space dimension
            multi_objective_index: Index for multi-objective optimization
            
        Returns:
            SAC agent with optimized hyperparameters
        """
        if self.best_params is None:
            raise ValueError("No optimization results available")
        
        if self.config.directions:  # Multi-objective
            params = self.best_params[multi_objective_index]
        else:
            params = self.best_params
        
        return SACAgent(
            state_dim=state_dim,
            action_dim=action_dim,
            config=params
        )
    
    def load_study(self, study_path: str) -> None:
        """Load previously saved study.
        
        Args:
            study_path: Path to saved study
        """
        import pickle
        
        with open(study_path, 'rb') as f:
            self.study = pickle.load(f)
        
        if self.config.directions:  # Multi-objective
            self.best_params = [trial.params for trial in self.study.best_trials]
        else:
            self.best_params = self.study.best_params
        
        self.logger.info(f"Study loaded from {study_path}")
    
    def continue_optimization(
        self,
        objective: ObjectiveFunction,
        additional_trials: int
    ) -> Dict[str, Any]:
        """Continue optimization with additional trials.
        
        Args:
            objective: Objective function
            additional_trials: Number of additional trials
            
        Returns:
            Updated optimization results
        """
        if self.study is None:
            raise ValueError("No study to continue. Run optimize() first.")
        
        self.logger.info(f"Continuing optimization with {additional_trials} additional trials")
        
        # Update config
        original_trials = self.config.n_trials
        self.config.n_trials = additional_trials
        
        # Continue optimization
        self.study.optimize(
            objective,
            n_trials=additional_trials,
            timeout=self.config.timeout,
            n_jobs=self.config.n_jobs
        )
        
        # Update best parameters
        if self.config.directions:  # Multi-objective
            self.best_params = [trial.params for trial in self.study.best_trials]
        else:
            self.best_params = self.study.best_params
        
        # Restore original config
        self.config.n_trials = original_trials + additional_trials
        
        return {
            'success': True,
            'total_trials': len(self.study.trials),
            'additional_trials': additional_trials,
            'best_params': self.best_params
        }