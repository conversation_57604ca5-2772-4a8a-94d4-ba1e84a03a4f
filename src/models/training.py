"""Training utilities for FinRL trading models.

This module provides:
- Model training orchestration
- Training progress monitoring
- Early stopping and checkpointing
- Training metrics collection
- Cross-validation utilities
- Training visualization
"""

import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Callable
from pathlib import Path
import json
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass, asdict, field
from tqdm import tqdm

from config import settings
from utils import (
    get_logger, log_performance, log_model_metrics, 
    log_error, LoggingContext
)
from models.sac_agent import SACAgent


@dataclass
class TrainingConfig:
    """Training configuration parameters."""
    total_timesteps: int = field(default_factory=lambda: __import__('config.settings', fromlist=['settings']).settings.sac.total_timesteps)
    eval_freq: int = 10000
    save_freq: int = 50000
    early_stopping_patience: int = 10
    early_stopping_threshold: float = 0.01
    checkpoint_dir: str = "models/checkpoints"
    log_interval: int = 1000
    eval_episodes: int = 5
    max_training_time: Optional[int] = None  # seconds
    warmup_steps: int = 1000
    validation_split: float = 0.2
    

@dataclass
class TrainingMetrics:
    """Training metrics container."""
    episode: int
    timestep: int
    episode_return: float
    episode_length: int
    actor_loss: Optional[float] = None
    critic_loss: Optional[float] = None
    alpha_loss: Optional[float] = None
    eval_return: Optional[float] = None
    eval_std: Optional[float] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()


class EarlyStopping:
    """Early stopping utility for training."""
    
    def __init__(
        self,
        patience: int = 10,
        threshold: float = 0.01,
        mode: str = 'max'
    ):
        """Initialize early stopping.
        
        Args:
            patience: Number of evaluations to wait
            threshold: Minimum improvement threshold
            mode: 'max' for maximizing metric, 'min' for minimizing
        """
        self.patience = patience
        self.threshold = threshold
        self.mode = mode
        self.best_score = None
        self.counter = 0
        self.early_stop = False
        
    def __call__(self, score: float) -> bool:
        """Check if training should stop early.
        
        Args:
            score: Current evaluation score
            
        Returns:
            True if training should stop
        """
        if self.best_score is None:
            self.best_score = score
        elif self._is_better(score, self.best_score):
            self.best_score = score
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        
        return self.early_stop
    
    def _is_better(self, current: float, best: float) -> bool:
        """Check if current score is better than best."""
        if self.mode == 'max':
            return current > best + self.threshold
        else:
            return current < best - self.threshold


class TrainingMonitor:
    """Monitor and log training progress."""
    
    def __init__(self, log_dir: str):
        """Initialize training monitor.
        
        Args:
            log_dir: Directory for training logs
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.metrics_history: List[TrainingMetrics] = []
        self.logger = get_logger(__name__)
        
        # Initialize metrics file
        self.metrics_file = self.log_dir / "training_metrics.jsonl"
        
    def log_metrics(self, metrics: TrainingMetrics) -> None:
        """Log training metrics.
        
        Args:
            metrics: Training metrics to log
        """
        self.metrics_history.append(metrics)
        
        # Write to file
        with open(self.metrics_file, 'a') as f:
            f.write(json.dumps(asdict(metrics)) + '\n')
        
        # Log to console
        log_model_metrics(
            'SAC',
            metrics.episode_return,
            metrics.episode_length,
            metrics.timestep
        )
    
    def get_latest_metrics(self, n: int = 10) -> List[TrainingMetrics]:
        """Get latest n training metrics.
        
        Args:
            n: Number of latest metrics to return
            
        Returns:
            List of latest training metrics
        """
        return self.metrics_history[-n:]
    
    def get_training_summary(self) -> Dict[str, Any]:
        """Get training summary statistics.
        
        Returns:
            Dictionary with training summary
        """
        if not self.metrics_history:
            return {}
        
        returns = [m.episode_return for m in self.metrics_history]
        lengths = [m.episode_length for m in self.metrics_history]
        
        return {
            'total_episodes': len(self.metrics_history),
            'total_timesteps': self.metrics_history[-1].timestep,
            'mean_return': np.mean(returns),
            'std_return': np.std(returns),
            'max_return': np.max(returns),
            'min_return': np.min(returns),
            'mean_length': np.mean(lengths),
            'training_duration': self._get_training_duration()
        }
    
    def _get_training_duration(self) -> str:
        """Calculate training duration."""
        if len(self.metrics_history) < 2:
            return "0:00:00"
        
        start_time = datetime.fromisoformat(self.metrics_history[0].timestamp)
        end_time = datetime.fromisoformat(self.metrics_history[-1].timestamp)
        duration = end_time - start_time
        
        return str(duration).split('.')[0]  # Remove microseconds
    
    def plot_training_progress(self, save_path: Optional[str] = None) -> None:
        """Plot training progress.
        
        Args:
            save_path: Path to save the plot
        """
        if not self.metrics_history:
            self.logger.warning("No metrics to plot")
            return
        
        # Prepare data
        df = pd.DataFrame([asdict(m) for m in self.metrics_history])
        
        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Training Progress', fontsize=16)
        
        # Episode returns
        axes[0, 0].plot(df['episode'], df['episode_return'])
        axes[0, 0].set_title('Episode Returns')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Return')
        axes[0, 0].grid(True)
        
        # Episode lengths
        axes[0, 1].plot(df['episode'], df['episode_length'])
        axes[0, 1].set_title('Episode Lengths')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Length')
        axes[0, 1].grid(True)
        
        # Rolling average returns
        window = min(50, len(df) // 4)
        if window > 1:
            rolling_returns = df['episode_return'].rolling(window=window).mean()
            axes[1, 0].plot(df['episode'], rolling_returns)
            axes[1, 0].set_title(f'Rolling Average Returns (window={window})')
            axes[1, 0].set_xlabel('Episode')
            axes[1, 0].set_ylabel('Average Return')
            axes[1, 0].grid(True)
        
        # Evaluation returns (if available)
        eval_data = df[df['eval_return'].notna()]
        if not eval_data.empty:
            axes[1, 1].plot(eval_data['episode'], eval_data['eval_return'], 'o-')
            axes[1, 1].fill_between(
                eval_data['episode'],
                eval_data['eval_return'] - eval_data['eval_std'],
                eval_data['eval_return'] + eval_data['eval_std'],
                alpha=0.3
            )
            axes[1, 1].set_title('Evaluation Returns')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Eval Return')
            axes[1, 1].grid(True)
        else:
            axes[1, 1].text(0.5, 0.5, 'No evaluation data', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
        
        plt.tight_layout()
        
        # Save or show
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"Training plot saved to {save_path}")
        else:
            plt.savefig(self.log_dir / "training_progress.png", dpi=300, bbox_inches='tight')
        
        plt.close()


class ModelTrainer:
    """Main training orchestrator for FinRL models."""
    
    def __init__(self, config: Optional[TrainingConfig] = None):
        """Initialize model trainer.
        
        Args:
            config: Training configuration
        """
        self.config = config or TrainingConfig()
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.monitor = TrainingMonitor(self.config.checkpoint_dir)
        self.early_stopping = EarlyStopping(
            patience=self.config.early_stopping_patience,
            threshold=self.config.early_stopping_threshold
        )
        
        # Training state
        self.training_start_time = None
        self.best_model_path = None
        
    @log_performance
    def train_agent(
        self,
        agent: SACAgent,
        train_env,
        eval_env=None,
        callbacks: Optional[List[Callable]] = None
    ) -> Dict[str, Any]:
        """Train the SAC agent with monitoring and checkpointing.
        
        Args:
            agent: SAC agent to train
            train_env: Training environment
            eval_env: Evaluation environment
            callbacks: Optional training callbacks
            
        Returns:
            Training results dictionary
        """
        self.training_start_time = datetime.now()
        callbacks = callbacks or []
        
        with LoggingContext("SAC Training"):
            self.logger.info(f"Starting training with config: {asdict(self.config)}")
            
            try:
                # Setup checkpoint directory
                checkpoint_dir = Path(self.config.checkpoint_dir)
                checkpoint_dir.mkdir(parents=True, exist_ok=True)
                
                # Training loop
                episode = 0
                timestep = 0
                best_eval_return = float('-inf')
                
                # Progress bar
                pbar = tqdm(total=self.config.total_timesteps, desc="Training")
                
                while timestep < self.config.total_timesteps:
                    # Check time limit
                    if self._check_time_limit():
                        self.logger.info("Training stopped due to time limit")
                        break
                    
                    # Train episode
                    episode_metrics = self._train_episode(
                        agent, train_env, episode, timestep
                    )
                    
                    timestep = episode_metrics.timestep
                    episode += 1
                    
                    # Update progress bar
                    pbar.update(episode_metrics.episode_length)
                    pbar.set_postfix({
                        'Episode': episode,
                        'Return': f"{episode_metrics.episode_return:.2f}",
                        'Length': episode_metrics.episode_length
                    })
                    
                    # Evaluation
                    if eval_env and timestep % self.config.eval_freq == 0:
                        eval_metrics = self._evaluate_agent(agent, eval_env)
                        episode_metrics.eval_return = eval_metrics['mean_return']
                        episode_metrics.eval_std = eval_metrics['std_return']
                        
                        # Check for best model
                        if eval_metrics['mean_return'] > best_eval_return:
                            best_eval_return = eval_metrics['mean_return']
                            self.best_model_path = checkpoint_dir / f"best_model_ep{episode}.pkl"
                            agent.save_model(self.best_model_path)
                            self.logger.info(f"New best model saved: {best_eval_return:.2f}")
                        
                        # Early stopping check
                        if self.early_stopping(eval_metrics['mean_return']):
                            self.logger.info("Early stopping triggered")
                            break
                    
                    # Log metrics
                    self.monitor.log_metrics(episode_metrics)
                    
                    # Checkpointing
                    if timestep % self.config.save_freq == 0:
                        checkpoint_path = checkpoint_dir / f"checkpoint_ep{episode}.pkl"
                        agent.save_model(checkpoint_path)
                        self.logger.info(f"Checkpoint saved: {checkpoint_path}")
                    
                    # Execute callbacks
                    for callback in callbacks:
                        callback(episode, timestep, episode_metrics)
                
                pbar.close()
                
                # Final evaluation
                if eval_env:
                    final_eval = self._evaluate_agent(agent, eval_env)
                    self.logger.info(f"Final evaluation: {final_eval['mean_return']:.2f} ± {final_eval['std_return']:.2f}")
                
                # Save final model
                final_model_path = checkpoint_dir / "final_model.pkl"
                agent.save_model(final_model_path)
                
                # Generate training report
                training_summary = self.monitor.get_training_summary()
                self.monitor.plot_training_progress()
                
                self.logger.success(f"Training completed: {episode} episodes, {timestep} timesteps")
                
                return {
                    'success': True,
                    'episodes': episode,
                    'timesteps': timestep,
                    'best_model_path': str(self.best_model_path) if self.best_model_path else None,
                    'final_model_path': str(final_model_path),
                    'training_summary': training_summary,
                    'best_eval_return': best_eval_return
                }
                
            except Exception as e:
                log_error("Training failed", e)
                return {
                    'success': False,
                    'error': str(e)
                }
    
    def _train_episode(
        self,
        agent: SACAgent,
        env,
        episode: int,
        current_timestep: int
    ) -> TrainingMetrics:
        """Train a single episode.
        
        Args:
            agent: SAC agent
            env: Training environment
            episode: Current episode number
            current_timestep: Current timestep
            
        Returns:
            Episode training metrics
        """
        state = env.reset()
        episode_return = 0
        episode_length = 0
        done = False
        
        while not done:
            # Select action
            action, _ = agent.predict(state, deterministic=False)
            
            # Take step
            next_state, reward, done, info = env.step(action)
            
            # Update metrics
            episode_return += reward
            episode_length += 1
            current_timestep += 1
            
            state = next_state
        
        return TrainingMetrics(
            episode=episode,
            timestep=current_timestep,
            episode_return=episode_return,
            episode_length=episode_length
        )
    
    def _evaluate_agent(
        self,
        agent: SACAgent,
        eval_env,
        n_episodes: Optional[int] = None
    ) -> Dict[str, float]:
        """Evaluate agent performance.
        
        Args:
            agent: SAC agent
            eval_env: Evaluation environment
            n_episodes: Number of evaluation episodes
            
        Returns:
            Evaluation metrics
        """
        n_episodes = n_episodes or self.config.eval_episodes
        return agent.evaluate_policy(eval_env, n_episodes, deterministic=True)
    
    def _check_time_limit(self) -> bool:
        """Check if training time limit is exceeded.
        
        Returns:
            True if time limit exceeded
        """
        if self.config.max_training_time is None or self.training_start_time is None:
            return False
        
        elapsed = (datetime.now() - self.training_start_time).total_seconds()
        return elapsed > self.config.max_training_time
    
    def resume_training(
        self,
        agent: SACAgent,
        checkpoint_path: str,
        train_env,
        eval_env=None
    ) -> Dict[str, Any]:
        """Resume training from checkpoint.
        
        Args:
            agent: SAC agent
            checkpoint_path: Path to checkpoint
            train_env: Training environment
            eval_env: Evaluation environment
            
        Returns:
            Training results
        """
        self.logger.info(f"Resuming training from {checkpoint_path}")
        
        # Load checkpoint
        agent.load_model(checkpoint_path)
        
        # Continue training
        return self.train_agent(agent, train_env, eval_env)
    
    def cross_validate(
        self,
        agent_factory: Callable,
        data_splits: List[Tuple],
        k_folds: int = 5
    ) -> Dict[str, Any]:
        """Perform k-fold cross-validation.
        
        Args:
            agent_factory: Function to create new agent instances
            data_splits: List of (train_env, eval_env) tuples
            k_folds: Number of folds
            
        Returns:
            Cross-validation results
        """
        self.logger.info(f"Starting {k_folds}-fold cross-validation")
        
        fold_results = []
        
        for fold in range(k_folds):
            self.logger.info(f"Training fold {fold + 1}/{k_folds}")
            
            # Create new agent for this fold
            agent = agent_factory()
            
            # Get data for this fold
            train_env, eval_env = data_splits[fold]
            
            # Train agent
            fold_result = self.train_agent(agent, train_env, eval_env)
            fold_result['fold'] = fold
            fold_results.append(fold_result)
        
        # Aggregate results
        successful_folds = [r for r in fold_results if r['success']]
        
        if successful_folds:
            eval_returns = [r['best_eval_return'] for r in successful_folds]
            cv_results = {
                'mean_performance': np.mean(eval_returns),
                'std_performance': np.std(eval_returns),
                'successful_folds': len(successful_folds),
                'total_folds': k_folds,
                'fold_results': fold_results
            }
        else:
            cv_results = {
                'mean_performance': 0.0,
                'std_performance': 0.0,
                'successful_folds': 0,
                'total_folds': k_folds,
                'fold_results': fold_results
            }
        
        self.logger.info(
            f"Cross-validation completed: {cv_results['mean_performance']:.2f} ± "
            f"{cv_results['std_performance']:.2f} ({cv_results['successful_folds']}/{k_folds} successful)"
        )
        
        return cv_results