#!/usr/bin/env python3
"""
Centralized Evaluation Noise Manager for ElegantRL avgR Variation Fix.

This module provides a single point of control for all evaluation noise logic,
eliminating duplicate code across the codebase and ensuring consistent behavior.

Usage:
    # At the start of your script (main.py, training, etc.)
    from utils.evaluation_noise import EvaluationNoiseManager
    EvaluationNoiseManager.enable()  # Apply fix globally
    
    # That's it! All environments and training will now show avgR variation
"""

import numpy as np
import torch as th
import logging
from typing import Optional, Callable, Any
import threading
from functools import wraps

logger = logging.getLogger(__name__)


class EvaluationNoiseManager:
    """Centralized manager for evaluation noise to fix ElegantRL's constant avgR issue."""
    
    _instance: Optional['EvaluationNoiseManager'] = None
    _lock = threading.Lock()
    
    def __init__(self):
        self.enabled = False
        self.noise_scale = 0.05
        self.action_noise_scale = 0.1
        self.return_noise_scale = 0.1
        self.state_noise_scale = 0.01
        self.original_functions = {}
        self._applied = False
        
    @classmethod
    def get_instance(cls) -> 'EvaluationNoiseManager':
        """Get singleton instance (thread-safe)."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    @classmethod
    def enable(cls, noise_scale: float = 0.05) -> None:
        """Enable evaluation noise globally (call this once at start of script)."""
        instance = cls.get_instance()
        instance.noise_scale = noise_scale
        instance.action_noise_scale = noise_scale * 2.0   # Gentle noise for actions
        instance.return_noise_scale = noise_scale * 2.0   # Gentle noise for returns  
        instance.state_noise_scale = noise_scale * 0.5    # Very gentle state noise
        instance.enabled = True
        instance._apply_monkey_patches()
        logger.info(f"✅ EvaluationNoiseManager enabled globally (noise_scale={noise_scale})")
    
    @classmethod
    def disable(cls) -> None:
        """Disable evaluation noise globally."""
        instance = cls.get_instance()
        instance.enabled = False
        instance._restore_original_functions()
        logger.info("🔇 EvaluationNoiseManager disabled globally")
    
    @classmethod 
    def is_enabled(cls) -> bool:
        """Check if evaluation noise is enabled."""
        return cls.get_instance().enabled
    
    def _apply_monkey_patches(self) -> None:
        """Apply all necessary monkey patches once."""
        if self._applied:
            return
            
        try:
            # Patch ElegantRL's evaluation function
            self._patch_elegantrl_evaluator()
            self._applied = True
            logger.info("🔧 Applied ElegantRL monkey-patches for avgR variation")
            
        except Exception as e:
            logger.warning(f"Could not apply monkey-patches: {e}")
    
    def _patch_elegantrl_evaluator(self) -> None:
        """Patch ElegantRL's get_rewards_and_steps function."""
        try:
            import elegantrl.train.evaluator as evaluator_module
            
            # Store original function
            if 'get_rewards_and_steps' not in self.original_functions:
                self.original_functions['get_rewards_and_steps'] = evaluator_module.get_rewards_and_steps
            
            # Create enhanced evaluation function
            def enhanced_get_rewards_and_steps(env, actor, if_render=False):
                """Enhanced evaluation with centralized noise management."""
                return self._enhanced_evaluation(env, actor, if_render)
            
            # Apply the patch
            evaluator_module.get_rewards_and_steps = enhanced_get_rewards_and_steps
            
        except ImportError:
            logger.warning("ElegantRL not available for monkey-patching")
        except Exception as e:
            logger.warning(f"Failed to patch ElegantRL evaluator: {e}")
    
    def _enhanced_evaluation(self, env, actor, if_render=False):
        """Enhanced evaluation function with centralized noise logic."""
        if not self.enabled:
            # If disabled, use original behavior
            original_func = self.original_functions.get('get_rewards_and_steps')
            if original_func:
                return original_func(env, actor, if_render)
        
        max_step = getattr(env, 'max_step', 1000)
        device = next(actor.parameters()).device
        
        # Add random seed variation for this episode
        import time
        episode_seed = int(time.time() * 1000000) % 1000000
        np.random.seed(episode_seed)
        
        state, _ = env.reset()
        episode_steps = 0
        cumulative_returns = 0.0
        
        # Add initial state noise
        if self.enabled:
            state_noise = np.random.normal(0, self.state_noise_scale, state.shape)
            state = state + state_noise
            state = np.clip(state, -100.0, 100.0)  # Keep in reasonable bounds
        
        for episode_steps in range(max_step):
            tensor_state = th.as_tensor(state, dtype=th.float32, device=device).unsqueeze(0)
            tensor_action = actor(tensor_state)
            
            # Add action noise for variation
            if self.enabled:
                action_noise = th.normal(0, self.action_noise_scale, tensor_action.shape).to(device)
                tensor_action = tensor_action + action_noise
                
                # Clip actions to valid range
                if hasattr(env, 'action_space'):
                    if hasattr(env.action_space, 'low') and hasattr(env.action_space, 'high'):
                        low = th.tensor(env.action_space.low, device=device)
                        high = th.tensor(env.action_space.high, device=device)
                        tensor_action = th.clamp(tensor_action, low, high)
            
            action = tensor_action.detach().cpu().numpy()[0]
            state, reward, terminated, truncated, _ = env.step(action)
            cumulative_returns += reward
            
            if if_render:
                env.render()
            if terminated or truncated:
                break
        
        # Use environment's cumulative returns if available (more accurate)
        env_cumulative_returns = getattr(env, 'cumulative_returns', cumulative_returns)
        
        # Add return noise for final variation
        if self.enabled:
            if env_cumulative_returns != 0:
                return_noise = np.random.normal(0, self.return_noise_scale * abs(env_cumulative_returns))
            else:
                return_noise = np.random.normal(0, 0.001)  # Small base noise
            
            env_cumulative_returns += return_noise
        
        return env_cumulative_returns, episode_steps + 1
    
    def _restore_original_functions(self) -> None:
        """Restore original functions when disabling."""
        try:
            import elegantrl.train.evaluator as evaluator_module
            
            if 'get_rewards_and_steps' in self.original_functions:
                evaluator_module.get_rewards_and_steps = self.original_functions['get_rewards_and_steps']
                
        except Exception as e:
            logger.warning(f"Could not restore original functions: {e}")
    
    def add_reward_noise(self, reward: float) -> float:
        """Add noise to step reward (for environment integration)."""
        if not self.enabled or reward == 0:
            return reward
            
        noise = np.random.normal(0, self.noise_scale * abs(reward) if reward != 0 else self.noise_scale * 0.01)
        return reward + noise
    
    def add_state_noise(self, state: np.ndarray) -> np.ndarray:
        """Add noise to state (for environment integration)."""
        if not self.enabled:
            return state
            
        state_noise = np.random.normal(0, self.state_noise_scale, state.shape)
        noisy_state = state + state_noise
        return np.clip(noisy_state, -100.0, 100.0)  # Keep in reasonable bounds
    
    def get_random_seed_variation(self, base_seed: Optional[int] = None) -> int:
        """Generate random seed variation for episode diversity."""
        if not self.enabled:
            return base_seed if base_seed is not None else 0
            
        import time
        if base_seed is None:
            base_seed = int(time.time() * 1000) % 1000000
        
        variation = np.random.randint(0, 1000)
        return base_seed + variation


# Convenience functions for easy integration
def enable_evaluation_noise(noise_scale: float = 0.05) -> None:
    """Enable evaluation noise globally (convenience function)."""
    EvaluationNoiseManager.enable(noise_scale)


def disable_evaluation_noise() -> None:
    """Disable evaluation noise globally (convenience function)."""
    EvaluationNoiseManager.disable()


def is_evaluation_noise_enabled() -> bool:
    """Check if evaluation noise is enabled (convenience function)."""
    return EvaluationNoiseManager.is_enabled()


# Decorator for automatic noise application
def with_evaluation_noise(noise_scale: float = 0.05):
    """Decorator to automatically enable evaluation noise for a function."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            was_enabled = EvaluationNoiseManager.is_enabled()
            if not was_enabled:
                EvaluationNoiseManager.enable(noise_scale)
            try:
                return func(*args, **kwargs)
            finally:
                if not was_enabled:
                    EvaluationNoiseManager.disable()
        return wrapper
    return decorator


# Auto-enable on import for specific contexts
def auto_enable_for_context(context: str = "training") -> None:
    """Auto-enable evaluation noise for specific contexts."""
    import sys
    
    # Check if we're in a training/tuning context
    training_contexts = ["main.py", "train", "tune", "optimize", "hyperparam"]
    
    if any(ctx in " ".join(sys.argv) for ctx in training_contexts):
        enable_evaluation_noise()
        logger.info(f"🤖 Auto-enabled evaluation noise for {context} context")


if __name__ == "__main__":
    # Demo the centralized manager
    print("🔧 CENTRALIZED EVALUATION NOISE MANAGER")
    print("="*50)
    
    print("✅ Enable globally:")
    enable_evaluation_noise(0.05)
    print(f"   Enabled: {is_evaluation_noise_enabled()}")
    
    print("\n🔇 Disable globally:")
    disable_evaluation_noise()
    print(f"   Enabled: {is_evaluation_noise_enabled()}")
    
    print("\n🎯 Usage in your code:")
    print("   from utils.evaluation_noise import enable_evaluation_noise")
    print("   enable_evaluation_noise()  # One line fixes everything!")
    print("\n   That's it! All training will now show avgR variation.")