"""Logging Utilities for FinRL Trading Agent

This module provides comprehensive logging setup using loguru with support for
both console and file logging, structured logging, and performance monitoring.
"""

import sys
from pathlib import Path
from typing import Optional, Dict, Any

import logging # Added for standard library logging interception
from loguru import logger

from config.constants import LOG_FORMATS

# Intercept standard logging
class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level,
            record.getMessage(),
        )


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    rotation: str = "10 MB",
    retention: str = "30 days",
    format_type: str = "detailed",
    enable_console: bool = True,
    enable_file: bool = True,
    worker_id: Optional[str] = None
) -> None:
    """Setup comprehensive logging configuration
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional)
        rotation: Log file rotation size
        retention: Log file retention period
        format_type: Log format type (detailed, simple, json)
        enable_console: Enable console logging
        enable_file: Enable file logging
        worker_id: Worker ID for unique log file naming
    """
    import os
    current_pid = os.getpid()
    
    # Remove default handler
    logger.remove()

    # Configure standard logging to use the InterceptHandler
    # This will route all standard library logs through Loguru
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    # force=True is important if basicConfig might have been called before
    
    # Get format string
    log_format = LOG_FORMATS.get(format_type, LOG_FORMATS['detailed'])
    
    # Console logging
    if enable_console:
        try:
            logger.add(
                sys.stdout,
                level=level,
                format=log_format,
                colorize=True,
                backtrace=True,
                diagnose=True
            )
            print(f"[PID:{current_pid}] Console logging enabled successfully")
        except Exception as e:
            print(f"[PID:{current_pid}] Failed to setup console logging: {e}")
    
    # File logging
    effective_log_file = None
    if enable_file and log_file:
        try:
            log_path = Path(log_file)
            # Construct unique log file name if worker_id is provided
            if worker_id:
                log_file_name = f"{log_path.stem}_{worker_id}{log_path.suffix}"
                effective_log_file = log_path.parent / log_file_name
            else:
                effective_log_file = log_path

            print(f"[PID:{current_pid}] Attempting to create log file: {effective_log_file}")
            
            # Ensure log directory exists with robust error handling
            log_dir = effective_log_file.parent
            try:
                log_dir.mkdir(parents=True, exist_ok=True)
                print(f"[PID:{current_pid}] Log directory created/verified: {log_dir}")
            except Exception as dir_error:
                print(f"[PID:{current_pid}] Failed to create log directory {log_dir}: {dir_error}")
                raise
            
            # Test write permissions
            try:
                test_file = effective_log_file.with_suffix('.test')
                test_file.touch()
                test_file.unlink()
                print(f"[PID:{current_pid}] Write permissions verified for log directory")
            except Exception as perm_error:
                print(f"[PID:{current_pid}] Write permission test failed: {perm_error}")
                raise
            
            # Add file handler with comprehensive error handling
            try:
                logger.add(
                    effective_log_file,
                    level=level,
                    format=log_format,
                    rotation=rotation,
                    retention=retention,
                    compression="zip",
                    backtrace=True,
                    diagnose=True,
                    enqueue=True  # Thread-safe logging
                )
                print(f"[PID:{current_pid}] File logging enabled successfully: {effective_log_file}")
                
                # Verify the log file was actually created
                if effective_log_file.exists():
                    print(f"[PID:{current_pid}] Log file verified to exist: {effective_log_file}")
                else:
                    print(f"[PID:{current_pid}] WARNING: Log file does not exist after creation: {effective_log_file}")
                    
            except Exception as handler_error:
                print(f"[PID:{current_pid}] Failed to add file handler: {handler_error}")
                raise
                
        except Exception as file_error:
            print(f"[PID:{current_pid}] File logging setup failed: {file_error}")
            print(f"[PID:{current_pid}] Continuing with console-only logging")
            effective_log_file = None
    
    # Log initialization message
    final_log_file = str(effective_log_file) if effective_log_file else 'None'
    logger.info(f"[PID:{current_pid}] Logging initialized - Level: {level}, File: {final_log_file}, Worker ID: {worker_id}")
    
    # Additional debug information
    if worker_id:
        logger.info(f"[PID:{current_pid}] Worker logging setup complete - Worker ID: {worker_id}")
    
    return effective_log_file

class LevelLogger:
    """Logger wrapper that respects log level settings."""
    
    def __init__(self, name: str, level: str = "INFO"):
        """Initialize level-aware logger.
        
        Args:
            name: Logger name
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        self.logger = logger.bind(name=name)
        self.level = level.upper()
        self.level_no = logger.level(self.level).no
    
    def _should_log(self, level: str) -> bool:
        """Check if message at given level should be logged."""
        try:
            msg_level_no = logger.level(level.upper()).no
            return msg_level_no >= self.level_no
        except ValueError:
            return True  # If level conversion fails, allow logging
    
    def debug(self, message: str, *args, **kwargs):
        """Log debug message if level allows."""
        if self._should_log("DEBUG"):
            self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """Log info message if level allows."""
        if self._should_log("INFO"):
            self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """Log warning message if level allows."""
        if self._should_log("WARNING"):
            self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """Log error message if level allows."""
        if self._should_log("ERROR"):
            self.logger.error(message, *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs):
        """Log exception message if level allows."""
        if self._should_log("ERROR"):
            self.logger.exception(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """Log critical message if level allows."""
        if self._should_log("CRITICAL"):
            self.logger.critical(message, *args, **kwargs)
    
    def success(self, message: str, *args, **kwargs):
        """Log success message if level allows."""
        if self._should_log("SUCCESS"):
            self.logger.success(message, *args, **kwargs)


def get_logger(name: str, level: Optional[str] = None) -> LevelLogger:
    """Get a configured logger by name.
    
    Args:
        name: Logger name
        level: Optional specific log level for this logger
        
    Returns:
        Configured logger instance
    """
    # Default to global logging level from settings if no level provided
    if level is None:
        try:
            from config.settings import settings
            level = settings.logging.level
        except ImportError:
            # Fallback to INFO if settings can't be imported
            level = "INFO"
    
    # Return a level-aware logger
    return LevelLogger(name, level)


def log_function_call(func_name: str, **kwargs) -> None:
    """Log function call with parameters
    
    Args:
        func_name: Name of the function being called
        **kwargs: Function parameters to log
    """
    params = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
    logger.debug(f"Calling {func_name}({params})")


def log_performance(operation: str, duration: float, **metrics) -> None:
    """Log performance metrics
    
    Args:
        operation: Name of the operation
        duration: Operation duration in seconds
        **metrics: Additional performance metrics
    """
    metric_str = ", ".join([f"{k}={v}" for k, v in metrics.items()])
    logger.info(f"Performance - {operation}: {duration:.3f}s, {metric_str}")


def log_data_quality(symbol: str, data_points: int, missing_ratio: float, **quality_metrics) -> None:
    """Log data quality metrics
    
    Args:
        symbol: Stock symbol
        data_points: Number of data points
        missing_ratio: Ratio of missing data
        **quality_metrics: Additional quality metrics
    """
    metrics_str = ", ".join([f"{k}={v}" for k, v in quality_metrics.items()])
    logger.info(
        f"Data Quality - {symbol}: {data_points} points, "
        f"{missing_ratio:.2%} missing, {metrics_str}"
    )


def log_trading_action(action: str, symbol: str, quantity: float, price: float, **details) -> None:
    """Log trading actions
    
    Args:
        action: Trading action (BUY, SELL, HOLD)
        symbol: Stock symbol
        quantity: Trade quantity
        price: Trade price
        **details: Additional trade details
    """
    details_str = ", ".join([f"{k}={v}" for k, v in details.items()])
    logger.info(
        f"Trading - {action} {quantity:.2f} {symbol} @ ${price:.2f}, {details_str}"
    )


def log_model_metrics(epoch: int, metrics: Dict[str, float]) -> None:
    """Log model training metrics
    
    Args:
        epoch: Training epoch
        metrics: Dictionary of metrics
    """
    metrics_str = ", ".join([f"{k}={v:.4f}" for k, v in metrics.items()])
    logger.info(f"Training - Epoch {epoch}: {metrics_str}")


def log_backtest_results(results: Dict[str, Any]) -> None:
    """Log backtesting results
    
    Args:
        results: Dictionary of backtest results
    """
    logger.info("Backtest Results:")
    for key, value in results.items():
        if isinstance(value, float):
            logger.info(f"  {key}: {value:.4f}")
        else:
            logger.info(f"  {key}: {value}")


def log_error_with_context(message: str, error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
    """Log error with additional context
    
    Args:
        message: Error message
        error: Exception that occurred
        context: Additional context information
    """
    if context is None:
        context = {}
    context_str = ", ".join([f"{k}={v}" for k, v in context.items()])
    logger.error(f"{message}: {context_str}")
    logger.exception(error)


class LoggingContext:
    """Context manager for structured logging with additional context"""
    
    def __init__(self, operation: str, **context):
        self.operation = operation
        self.context = context
        self.start_time = None
    
    def __enter__(self):
        import time
        self.start_time = time.time()
        context_str = ", ".join([f"{k}={v}" for k, v in self.context.items()])
        logger.info(f"Starting {self.operation} | Context: {context_str}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        duration = time.time() - self.start_time
        
        if exc_type is None:
            logger.success(f"Completed {self.operation} in {duration:.3f}s")
        else:
            logger.error(f"Failed {self.operation} after {duration:.3f}s: {exc_val}")
        
        return False  # Don't suppress exceptions


def configure_third_party_loggers():
    """Configure third-party library loggers to reduce noise"""
    import logging
    
    # Reduce noise from common libraries
    noisy_loggers = [
        'urllib3.connectionpool',
        'requests.packages.urllib3',
        'matplotlib',
        'PIL',
        'alpaca_trade_api'
    ]
    
    for logger_name in noisy_loggers:
        logging.getLogger(logger_name).setLevel(logging.WARNING)


import time
import functools

def log_performance(func):
    """Decorator to log performance metrics of a function or method.

    Args:
        func: The function or method to be decorated.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        operation_name = func.__qualname__ if hasattr(func, '__qualname__') else func.__name__
        logger.info(f"Performance - {operation_name}: {duration:.3f}s")
        return result
    return wrapper


# Initialize third-party logger configuration
configure_third_party_loggers()


def get_standard_logger(name: str, level: str = "INFO") -> logging.Logger:
    """Get a standard Python logger that can be pickled for multiprocessing.

    This is specifically for objects that need to be serialized/pickled
    for multiprocessing, as loguru loggers cannot be pickled.

    Args:
        name: Logger name
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

    Returns:
        Standard Python logger instance that can be pickled
    """
    import logging
    import sys
    from pathlib import Path

    # Create logger
    std_logger = logging.getLogger(name)
    std_logger.setLevel(getattr(logging, level.upper()))

    # Clear existing handlers to avoid duplicates
    std_logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Add console handler
    console_handler = logging.StreamHandler(sys.stderr)
    console_handler.setFormatter(formatter)
    std_logger.addHandler(console_handler)

    # Add file handler if log directory exists
    log_dir = Path("logs")
    if log_dir.exists():
        log_file = log_dir / f"{name.lower().replace('.', '_')}_mp.log"
        file_handler = logging.FileHandler(str(log_file))
        file_handler.setFormatter(formatter)
        std_logger.addHandler(file_handler)

    # Prevent propagation to root logger
    std_logger.propagate = False

    return std_logger
