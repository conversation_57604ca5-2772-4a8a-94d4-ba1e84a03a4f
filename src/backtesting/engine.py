"""Backtesting engine for evaluating trading strategies."""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
import json
import warnings

# Pyfolio imports
try:
    import pyfolio as pf
    import matplotlib.pyplot as plt
    import matplotlib
    import yfinance as yf
    matplotlib.use('Agg')  # Use non-interactive backend
    PYFOLIO_AVAILABLE = True
except ImportError:
    PYFOLIO_AVAILABLE = False
    warnings.warn("Pyfolio not available. Install with: pip install pyfolio")

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from trading.asymmetric_env import AsymmetricTradingEnv
from models.sac_agent import SACAgent
from data.processor import DataProcessor
from data.fetcher import DataFetcher
from strategies.asymmetric_strategy import AsymmetricConfig
from utils.logging import get_logger

logger = get_logger(__name__)


class BacktestEngine:
    """Engine for running backtests on trained trading models."""
    
    def __init__(self, settings):
        """Initialize the backtest engine.
        
        Args:
            settings: Configuration settings object
        """
        self.settings = settings
        self.results = {}
        
    def run_backtest(
        self, 
        model_path: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """Run a backtest on the specified model and data.
        
        Args:
            model_path: Path to the trained model (optional)
            start_date: Start date for backtest period
            end_date: End date for backtest period  
            output_dir: Directory to save results
            
        Returns:
            Dictionary containing backtest results
        """
        logger.info("Starting backtest execution")
        
        try:
            # Load and prepare data
            data = self._load_backtest_data(start_date, end_date)
            logger.info(f"Loaded backtest data: {len(data)} records")
            
            # Initialize environment
            env = self._create_backtest_environment(data)
            logger.info("Created backtest environment")
            
            # Load model if provided, otherwise try to find default model
            if model_path:
                model = self._load_model(model_path)
                logger.info(f"Loaded model from {model_path}")
            else:
                # Try to find and load default model
                default_model_path = self._find_default_model()
                if default_model_path:
                    model = self._load_model(default_model_path)
                    logger.info(f"Loaded default model from {default_model_path}")
                else:
                    logger.warning("No model provided and no default model found, using random actions for demonstration")
                    model = None
            
            # Run simulation
            results = self._run_simulation(env, model)
            logger.info("Completed backtest simulation")
            
            # Calculate performance metrics
            metrics = self._calculate_metrics(results)
            logger.info("Calculated performance metrics")
            
            # Prepare final results
            final_results = {
                'backtest_config': {
                    'model_path': model_path,
                    'start_date': start_date,
                    'end_date': end_date,
                    'data_records': len(data)
                },
                'performance_metrics': metrics,
                'trading_history': results.get('actions', []),
                'portfolio_values': results.get('portfolio_values', []),
                'timestamp': datetime.now().isoformat()
            }
            
            # Save results if output directory specified
            if output_dir:
                self._save_results(final_results, output_dir)
                
            self.results = final_results
            return final_results
            
        except Exception as e:
            logger.error(f"Backtest failed: {str(e)}")
            raise
    
    def _load_backtest_data(self, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Load and prepare data for backtesting."""
        # Try to load processed data first
        processed_data_path = Path(self.settings.data.processed_dir) / "processed_data.csv"
        
        if processed_data_path.exists():
            logger.info(f"Loading processed data from {processed_data_path}")
            data = pd.read_csv(processed_data_path)
            
            # Handle both 'date' (lowercase) and 'Date' (uppercase) columns
            date_column = None
            if 'date' in data.columns:
                date_column = 'date'
            elif 'Date' in data.columns:
                date_column = 'Date'
            
            if date_column:
                data[date_column] = pd.to_datetime(data[date_column])
                
                # Filter by date range if specified
                if start_date:
                    start_dt = pd.to_datetime(start_date)
                    data = data[data[date_column] >= start_dt]
                if end_date:
                    end_dt = pd.to_datetime(end_date)
                    data = data[data[date_column] <= end_dt]
            else:
                logger.warning("No date column found in processed data")
                
            return data
        else:
            logger.warning("No processed data found, fetching fresh data")
            # Fallback to fetching fresh data
            fetcher = DataFetcher()
            processor = DataProcessor(
                fetcher=fetcher,
                tech_indicator_list=self.settings.data.tech_indicator_list,
                vix_features=self.settings.data.vix_features,
                include_vix=self.settings.data.include_vix
            )
            
            # This is a simplified version - in practice you'd want to fetch all symbols
            symbol = self.settings.data.symbols[0] if self.settings.data.symbols else 'AAPL'
            raw_data = fetcher.fetch_symbol_data(
                symbol=symbol,
                start_date=start_date or self.settings.data.train_start_date,
                end_date=end_date or self.settings.data.test_end_date
            )
            
            return processor.process_stock_data(raw_data)
    
    def _prepare_finrl_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform data from long format to FinRL-compatible format.
        
        FinRL expects the DataFrame to be structured so that when you do df.loc[day, :],
        you get a row with data for all stocks for that day.
        """
        # Ensure date column is datetime and handle timezone-aware datetimes
        if 'date' in df.columns:
            # Convert timezone-aware datetime to UTC then remove timezone
            df['date'] = pd.to_datetime(df['date'], utc=True).dt.tz_localize(None)
        
        # Rename columns to match FinRL expectations (lowercase)
        # Keep all technical indicators and just convert to lowercase
        column_mapping = {
            'symbol': 'tic',
            'Open': 'open',
            'High': 'high', 
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume'
        }
        
        # Add all technical indicators from settings to mapping (convert to lowercase)
        # Fix: Check actual data columns instead of settings names since pandas_ta generates uppercase
        for col in df.columns:
            if col in self.settings.data.tech_indicator_list:
                column_mapping[col] = col.lower()
        
        # Apply column renaming
        df = df.rename(columns=column_mapping)
        
        # Sort by date and tic
        df = df.sort_values(['date', 'tic']).reset_index(drop=True)
        
        # Create a proper index for FinRL
        # FinRL uses integer index where each index represents a time step
        df['day'] = df['date'].factorize()[0]
        
        # Set the day as index for FinRL's df.loc[day, :] operation
        df = df.set_index('day')
        
        return df
    
    def _create_backtest_environment(self, data: pd.DataFrame) -> AsymmetricTradingEnv:
        """Create trading environment for backtesting."""
        # Apply FinRL data preparation (same as training)
        logger.info("Preparing data for FinRL environment compatibility")
        data = self._prepare_finrl_data(data.copy())
        
        # Remove duplicate columns if any
        data = data.loc[:, ~data.columns.duplicated(keep='first')]
        
        # Ensure 'tic' column exists
        if 'tic' not in data.columns and 'symbol' in data.columns:
            data['tic'] = data['symbol']
        
        # Calculate dimensions similar to main.py
        stock_dim = len(data['tic'].unique()) if 'tic' in data.columns else 1
        stock_symbols = data['tic'].unique().tolist() if 'tic' in data.columns else ['AAPL']
        
        # Calculate state space to match training setup exactly
        tech_indicators_per_stock = len(self.settings.data.tech_indicator_list)
        
        # Basic state: [balance, stock_shares, stock_prices, tech_indicators]
        # balance(1) + stock_shares(stock_dim) + stock_prices(stock_dim) + tech_indicators(stock_dim * tech_indicators_per_stock)
        base_state_space = 1 + 2 * stock_dim + (stock_dim * tech_indicators_per_stock)
        
        # Add asymmetric features: 3 features per stock (volatility, momentum, asymmetry_score)
        # This must match the AsymmetricTradingEnv._get_asymmetric_state_features implementation
        asymmetric_features = 3 * stock_dim
        
        # Enhanced state space (used when enhanced_state_input=True)
        enhanced_state_space = base_state_space + asymmetric_features
        
        # Add VIX features if included
        if self.settings.data.include_vix:
            enhanced_state_space += len(self.settings.data.vix_features)
        
        # Use enhanced state space for consistency with training
        state_space = enhanced_state_space
        
        # Log state space calculation details for debugging
        logger.info(f"State space calculation: stock_dim={stock_dim}, tech_indicators={tech_indicators_per_stock}")
        logger.info(f"  base_state_space={base_state_space} (1 + 2*{stock_dim} + {stock_dim}*{tech_indicators_per_stock})")
        logger.info(f"  asymmetric_features={asymmetric_features} (3*{stock_dim})")
        logger.info(f"  enhanced_state_space={enhanced_state_space} (base + asymmetric)")
        
        # Action space: one action per stock (buy/sell/hold)
        action_space = stock_dim
        
        # Create asymmetric config
        asymmetric_config = AsymmetricConfig(
            symbols=stock_symbols,
            base_position_size=getattr(self.settings.asymmetric, 'base_position_size', 0.05),
            max_asymmetric_multiplier=getattr(self.settings.asymmetric, 'max_asymmetric_multiplier', 2.0),
            min_asymmetric_multiplier=getattr(self.settings.asymmetric, 'min_asymmetric_multiplier', 0.5),
            asymmetric_stop_loss=getattr(self.settings.asymmetric, 'asymmetric_stop_loss', 0.03),
            asymmetric_take_profit=getattr(self.settings.asymmetric, 'asymmetric_take_profit', 0.06),
            signal_threshold=getattr(self.settings.asymmetric, 'signal_threshold', 0.5)
        )
        
        # Apply technical indicator preprocessing like in training
        logger.info("Applying NaN fill and type check for technical indicators in backtest data.")
        for col in self.settings.data.tech_indicator_list:
            # Check for lowercase version since prepare_finrl_data converts to lowercase
            col_lower = col.lower()
            if col_lower in data.columns:
                column_data_to_convert = data[col_lower]
                if isinstance(column_data_to_convert, pd.DataFrame):
                    logger.warning(f"Column '{col_lower}' is a DataFrame, likely due to duplicate names. Selecting first column for numeric conversion.")
                    original_nan_count = column_data_to_convert.isnull().sum().sum()
                    column_data_to_convert = column_data_to_convert.iloc[:, 0]
                else:
                    original_nan_count = column_data_to_convert.isnull().sum()
                
                data[col_lower] = pd.to_numeric(column_data_to_convert, errors='coerce')
                data_for_coercion_check = data[col_lower]
                if isinstance(data_for_coercion_check, pd.DataFrame):
                    logger.warning(f"Column '{col_lower}' in backtest data is a DataFrame when calculating coercion_nan_count.")
                    coercion_nan_count = data_for_coercion_check.isnull().sum().sum()
                else:
                    coercion_nan_count = data_for_coercion_check.isnull().sum()
                
                if coercion_nan_count > original_nan_count:
                    logger.warning(f"Tech indicator '{col_lower}' in backtest data had values coerced to NaN by pd.to_numeric.")
                if bool(data[col_lower].isnull().all()) and original_nan_count < len(data[col_lower]):
                    logger.warning(f"Tech indicator '{col_lower}' in backtest data became all NaNs after pd.to_numeric.")
                data[col_lower] = data[col_lower].fillna(0)
            else:
                logger.error(f"CRITICAL: Technical indicator '{col}' (lowercase: '{col_lower}') NOT FOUND in backtest DataFrame. Adding as zeros.")
                data[col_lower] = 0.0
        
        # Create environment configuration matching training setup
        env_config = {
            'df': data,
            'stock_dim': stock_dim,
            'hmax': self.settings.environment_config.hmax,
            'initial_amount': self.settings.environment_config.initial_amount,
            'num_stock_shares': [0] * stock_dim,
            'buy_cost_pct': [self.settings.environment_config.transaction_cost_pct] * stock_dim,
            'sell_cost_pct': [self.settings.environment_config.transaction_cost_pct] * stock_dim,
            # Don't pass state_space - let the environment calculate it automatically
            'action_space': action_space,
            'tech_indicator_list': self.settings.data.tech_indicator_list,
            'reward_scaling': self.settings.environment_config.reward_scaling,
            'asymmetric_config': asymmetric_config,
            'enhanced_state_input': True,  # Use enhanced state like training
            'turbulence_threshold': getattr(self.settings.env, 'turbulence_threshold', 1.0),
            'risk_indicator_col': 'turbulence',
            'make_plots': False,
            'print_verbosity': 1000,
            'log_level': self.settings.logging.level or 'INFO'
        }
        
        # Debug: log sample of data structure
        logger.info(f"Data structure debug - first few rows by day:")
        for day in sorted(data.index.unique())[:3]:
            day_data = data.loc[day]
            if isinstance(day_data, pd.DataFrame):
                logger.info(f"  Day {day}: {len(day_data)} rows - {day_data['tic'].tolist() if 'tic' in day_data.columns else 'no tic'}")
                if 'close' in day_data.columns:
                    logger.info(f"    Close prices: {day_data['close'].tolist()}")
            else:
                logger.info(f"  Day {day}: Single row - {getattr(day_data, 'tic', 'no tic')}")
                if hasattr(day_data, 'close'):
                    logger.info(f"    Close price: {day_data.close}")
        
        logger.info(f"About to create AsymmetricTradingEnv for backtesting with config: stock_dim={stock_dim}, action_space={action_space}, df_shape={data.shape}")
        env = AsymmetricTradingEnv(**env_config)
        logger.info("AsymmetricTradingEnv for backtesting created successfully")
        
        return env
    
    def _find_default_model(self) -> Optional[str]:
        """Find the default model to use for backtesting.
        
        Priority:
        1. Model path from training_results.json
        2. Latest saved model in models/saved/
        3. Latest checkpoint model in models/checkpoints/
        
        Returns:
            Path to the default model, or None if no model found
        """
        models_dir = Path(self.settings.models.model_dir) if hasattr(self.settings, 'models') else Path("models")
        
        # First, check training_results.json for model_path
        training_results_path = models_dir / "checkpoints" / "training_results.json"
        if training_results_path.exists():
            try:
                with open(training_results_path, 'r') as f:
                    training_results = json.load(f)
                
                model_path = training_results.get('model_path')
                if model_path and Path(model_path).exists():
                    logger.info(f"Found model from training_results.json: {model_path}")
                    return str(model_path)
                elif model_path:
                    logger.warning(f"Model path from training_results.json does not exist: {model_path}")
            except (json.JSONDecodeError, FileNotFoundError, KeyError) as e:
                logger.warning(f"Could not read training_results.json: {e}")
        
        # Second, try to find saved models
        saved_dir = models_dir / "saved"
        if saved_dir.exists():
            # Look for .pkl files (SAC agent format)
            saved_models = list(saved_dir.glob("*.pkl"))
            if saved_models:
                # Return the most recently modified saved model
                latest_saved = max(saved_models, key=lambda p: p.stat().st_mtime)
                logger.info(f"Found latest saved model: {latest_saved}")
                return str(latest_saved)
        
        # If no saved models, try checkpoints
        checkpoints_dir = models_dir / "checkpoints"
        if checkpoints_dir.exists():
            # Look for .pth files (PyTorch checkpoints)
            checkpoint_files = list(checkpoints_dir.glob("*.pth"))
            if checkpoint_files:
                # Return the most recently modified checkpoint
                latest_checkpoint = max(checkpoint_files, key=lambda p: p.stat().st_mtime)
                logger.info(f"Found latest checkpoint: {latest_checkpoint}")
                return str(latest_checkpoint)
            
            # Also check for .pt files (alternative PyTorch format)
            pt_files = list(checkpoints_dir.glob("*.pt"))
            if pt_files:
                latest_pt = max(pt_files, key=lambda p: p.stat().st_mtime)
                logger.info(f"Found latest .pt checkpoint: {latest_pt}")
                return str(latest_pt)
        
        logger.info("No saved models or checkpoints found")
        return None
    
    def _load_model(self, model_path: str):
        """Load a trained model for backtesting."""
        logger.info(f"Loading model from {model_path}")
        
        model_path = Path(model_path)
        
        if not model_path.exists():
            logger.error(f"Model file not found: {model_path}")
            return None
        
        try:
            if model_path.suffix == '.pkl':
                # Load SAC agent from pickle file
                from models.sac_agent import SACAgent
                
                # We need to determine state and action dimensions
                # For now, use default values - this could be improved by storing dimensions in metadata
                agent = SACAgent(state_dim=1, action_dim=1)  # Placeholder dimensions
                agent.load_model(str(model_path))
                logger.info(f"Successfully loaded SAC agent from {model_path}")
                return agent
                
            elif model_path.suffix in ['.pth', '.pt']:
                # Load PyTorch checkpoint
                import torch
                device = 'cuda' if torch.cuda.is_available() else 'cpu'
                checkpoint = torch.load(model_path, map_location=device, weights_only=False)
                logger.info(f"Successfully loaded PyTorch checkpoint from {model_path}")
                # For now, return the checkpoint dict - this would need proper model reconstruction
                return checkpoint
                
            else:
                logger.warning(f"Unsupported model format: {model_path.suffix}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to load model from {model_path}: {str(e)}")
            return None
    
    def _predict_action(self, model, state, env):
        """Predict action using the loaded model.
        
        Args:
            model: The loaded model (SAC agent, PyTorch checkpoint, or other)
            state: Current environment state
            env: Trading environment
            
        Returns:
            Action to take
        """
        try:
            # Handle SAC agent
            if hasattr(model, 'predict'):
                return model.predict(state)
            
            # Handle PyTorch checkpoint (dict)
            elif isinstance(model, dict):
                # For PyTorch checkpoints, we'd need to reconstruct the model
                # For now, fall back to random action
                logger.warning("PyTorch checkpoint loaded but model reconstruction not implemented, using random action")
                return env.action_space.sample()
            
            # Handle other model types
            elif hasattr(model, '__call__'):
                # If model is callable, try to call it with state
                # Convert numpy array to tensor if needed for PyTorch models
                if isinstance(state, np.ndarray):
                    import torch
                    # Add batch dimension for PyTorch models (they expect [batch_size, state_dim])
                    state_tensor = torch.FloatTensor(state).unsqueeze(0)
                    result = model(state_tensor)
                    # Remove batch dimension from result if present
                    if hasattr(result, 'squeeze'):
                        return result.squeeze(0).detach().numpy()
                    return result
                else:
                    return model(state)
            
            else:
                logger.warning(f"Unknown model type: {type(model)}, using random action")
                return env.action_space.sample()
                
        except Exception as e:
            logger.error(f"Error predicting action: {str(e)}, using random action")
            return env.action_space.sample()
    
    def _run_simulation(self, env: AsymmetricTradingEnv, model=None) -> Dict[str, Any]:
        """Run the trading simulation."""
        logger.info("Running trading simulation")
        
        reset_result = env.reset()
        if isinstance(reset_result, tuple):
            # New gym API: (state, info)
            state, _ = reset_result
        else:
            # Old gym API: state
            state = reset_result
        done = False
        step = 0
        
        # Log initial state details and get actual initial portfolio value
        initial_portfolio_value = env.initial_amount  # Default fallback
        if hasattr(env, 'state') and env.state is not None and len(env.state) > 2*env.stock_dim:
            cash = env.state[0]
            holdings = env.state[1:env.stock_dim+1]
            prices = env.state[env.stock_dim+1:2*env.stock_dim+1]
            initial_portfolio_value = cash + np.sum(holdings * prices)
            logger.info(f"Initial state after reset:")
            logger.info(f"  Cash: ${cash:.2f}")
            logger.info(f"  Holdings: {holdings}")
            logger.info(f"  Prices: {prices}")
            logger.info(f"  Total Portfolio: ${initial_portfolio_value:.2f}")
        
        actions = []
        portfolio_values = [initial_portfolio_value]  # Use actual calculated portfolio value
        rewards = []
        
        while not done and step < 10000:  # Safety limit
            if model is not None:
                # Use trained model to select action
                action = self._predict_action(model, state, env)
            else:
                # Use random actions for demonstration
                action = env.action_space.sample()
            
            step_result = env.step(action)
            if len(step_result) == 5:
                # New gym API: (state, reward, terminated, truncated, info)
                state, reward, terminated, truncated, info = step_result
                done = terminated or truncated
            else:
                # Old gym API: (state, reward, done, info)
                state, reward, done, info = step_result
            
            actions.append(action.tolist() if hasattr(action, 'tolist') else action)

            # Get portfolio value from environment
            # Try multiple possible keys that FinRL might use
            current_portfolio_value = None

            # Method 1: Check info dict for various possible keys
            for key in ['total_asset', 'total_assets', 'portfolio_value', 'account_value']:
                if key in info and info[key] is not None:
                    current_portfolio_value = float(info[key])
                    break

            # Method 2: Try to get portfolio value directly from environment
            if current_portfolio_value is None:
                try:
                    if hasattr(env, 'asset_memory') and len(env.asset_memory) > 0:
                        current_portfolio_value = float(env.asset_memory[-1])
                    elif hasattr(env, 'portfolio_value'):
                        current_portfolio_value = float(env.portfolio_value)
                    elif hasattr(env, 'state') and env.state is not None:
                        # Calculate portfolio value from state (cash + holdings * prices)
                        cash = env.state[0]
                        holdings = env.state[1:env.stock_dim+1]
                        prices = env.state[env.stock_dim+1:2*env.stock_dim+1]
                        current_portfolio_value = float(cash + sum(holdings * prices))
                except Exception as e:
                    logger.debug(f"Could not extract portfolio value from environment: {e}")

            # Method 3: Fallback to previous value if all else fails
            if current_portfolio_value is None:
                current_portfolio_value = portfolio_values[-1]
                logger.warning(f"Could not determine portfolio value at step {step}, using previous value")

            portfolio_values.append(current_portfolio_value)
            rewards.append(reward)
            
            step += 1

            if step % 100 == 0:
                logger.debug(f"Simulation step {step}, portfolio value: {portfolio_values[-1]:.2f}")

            # Debug logging for first few steps to track portfolio changes
            if step <= 5:
                # Get detailed state information for debugging
                if hasattr(env, 'state') and env.state is not None and len(env.state) > 2*env.stock_dim:
                    cash = env.state[0]
                    holdings = env.state[1:env.stock_dim+1]
                    prices = env.state[env.stock_dim+1:2*env.stock_dim+1]
                    logger.info(f"Step {step}: Action={action}")
                    logger.info(f"  Cash: ${cash:.2f}")
                    logger.info(f"  Holdings: {holdings}")
                    logger.info(f"  Prices: {prices}")
                    logger.info(f"  Holdings*Prices: ${np.sum(holdings * prices):.2f}")
                    logger.info(f"  Total Portfolio: ${current_portfolio_value:.2f}")
                    logger.info(f"  Reward: {reward:.6f}")
                else:
                    logger.info(f"Step {step}: Action={action}, Reward={reward:.6f}, Portfolio={current_portfolio_value:.2f}")

        logger.info(f"Simulation completed after {step} steps")
        logger.info(f"Portfolio value progression: Initial={portfolio_values[0]:.2f}, Final={portfolio_values[-1]:.2f}")
        logger.info(f"Total return: {((portfolio_values[-1] / portfolio_values[0]) - 1) * 100:.4f}%")
        
        return {
            'actions': actions,
            'portfolio_values': portfolio_values,
            'rewards': rewards,
            'total_steps': step,
            'final_portfolio_value': portfolio_values[-1]
        }
    
    def _calculate_metrics(self, results: Dict[str, Any]) -> Dict[str, float]:
        """Calculate performance metrics from simulation results."""
        portfolio_values = np.array(results['portfolio_values'])
        
        if len(portfolio_values) < 2:
            return {'error': 'Insufficient data for metrics calculation'}
        
        # Calculate returns
        returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        # Basic metrics
        total_return = (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0]
        annualized_return = (1 + total_return) ** (252 / len(returns)) - 1 if len(returns) > 0 else 0
        volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0
        
        # Risk metrics
        sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
        max_drawdown = self._calculate_max_drawdown(portfolio_values)
        
        metrics = {
            'total_return': float(total_return),
            'annualized_return': float(annualized_return),
            'volatility': float(volatility),
            'sharpe_ratio': float(sharpe_ratio),
            'max_drawdown': float(max_drawdown),
            'final_portfolio_value': float(portfolio_values[-1]),
            'initial_portfolio_value': float(portfolio_values[0]),
            'total_trades': len(results.get('actions', [])),
            'simulation_days': len(portfolio_values) - 1
        }
        
        return metrics
    
    def _calculate_max_drawdown(self, portfolio_values: np.ndarray) -> float:
        """Calculate maximum drawdown."""
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        return float(np.min(drawdown))
    
    def _generate_pyfolio_charts(self, results: Dict[str, Any], output_dir: str, start_date: str, end_date: str) -> Optional[str]:
        """Generate comprehensive pyfolio performance charts and statistics.
        
        Args:
            results: Backtest results containing portfolio values
            output_dir: Directory to save charts and statistics
            start_date: Start date of backtest
            end_date: End date of backtest
            
        Returns:
            Path to generated chart file or None if failed
        """
        if not PYFOLIO_AVAILABLE:
            logger.warning("Pyfolio not available, skipping chart generation")
            return None
            
        try:
            portfolio_values = results.get('portfolio_values', [])
            if len(portfolio_values) < 2:
                logger.warning("Insufficient portfolio data for pyfolio charts")
                return None
                
            # Create date range for portfolio values
            if start_date and end_date:
                start_dt = pd.to_datetime(start_date)
                end_dt = pd.to_datetime(end_date)
                date_range = pd.date_range(start=start_dt, end=end_dt, periods=len(portfolio_values))
            else:
                # Fallback to simple date range - use business days for more realistic frequency
                date_range = pd.date_range(start='2023-01-01', periods=len(portfolio_values), freq='B')
            
            # Create returns series
            portfolio_series = pd.Series(portfolio_values, index=date_range, name='portfolio_value')
            returns = portfolio_series.pct_change().dropna()
            returns.name = 'returns'
            
            
            # Generate timestamp for filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = Path(output_dir)
            
            # Fetch benchmark data (SPY and QQQ)
            benchmark_data = self._fetch_benchmark_data(start_date, end_date, returns.index)
            
            # Extract primary benchmark for stats (SPY preferred, QQQ as fallback)
            benchmark_returns = None
            if benchmark_data:
                if 'SPY' in benchmark_data:
                    benchmark_returns = benchmark_data['SPY']
                elif 'QQQ' in benchmark_data:
                    benchmark_returns = benchmark_data['QQQ']
            
            # Generate comprehensive performance statistics
            stats_file = self._generate_performance_stats(returns, benchmark_returns, output_path, timestamp)
            
            # Create pyfolio tear sheet
            chart_file = output_path / f"backtest_pyfolio_{timestamp}.png"
            
            try:
                # Clean returns data for pyfolio tear sheet
                clean_returns = returns.dropna()
                clean_returns = clean_returns.replace([np.inf, -np.inf], np.nan).dropna()

                # Validate data quality
                has_insufficient_data = len(clean_returns) < 2
                has_zero_std = bool(clean_returns.std() == 0) if len(clean_returns) > 0 else True
                if has_insufficient_data or has_zero_std:
                    raise ValueError("Insufficient or invalid returns data for pyfolio tear sheet")

                # Ensure clean_returns is a proper pandas Series
                if not isinstance(clean_returns, pd.Series):
                    logger.warning(f"clean_returns is not a Series: {type(clean_returns)}, converting...")
                    clean_returns = pd.Series(clean_returns)
                
                # Ensure proper index type and frequency for pyfolio
                if not isinstance(clean_returns.index, pd.DatetimeIndex):
                    logger.warning("Converting returns index to DatetimeIndex for pyfolio compatibility")
                    clean_returns.index = pd.to_datetime(clean_returns.index)
                
                # Set frequency if not already set
                if hasattr(clean_returns.index, 'freq') and clean_returns.index.freq is None:
                    try:
                        clean_returns.index.freq = pd.infer_freq(clean_returns.index)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Could not infer frequency for returns: {e}")
                
                # Ensure returns are numeric and finite
                clean_returns = pd.to_numeric(clean_returns, errors='coerce').dropna()
                if len(clean_returns) < 2:
                    raise ValueError("Insufficient numeric returns data after cleaning")

                # Use pyfolio's built-in tear sheet functionality
                if benchmark_returns is not None and not benchmark_returns.empty:
                    # Clean benchmark data as well
                    clean_benchmark = benchmark_returns.dropna()
                    clean_benchmark = clean_benchmark.replace([np.inf, -np.inf], np.nan).dropna()
                    clean_benchmark = pd.to_numeric(clean_benchmark, errors='coerce').dropna()

                    # Align the cleaned data
                    aligned_returns, aligned_benchmark = clean_returns.align(clean_benchmark, join='inner')

                    # Check conditions safely to avoid pandas Series boolean ambiguity
                    has_sufficient_data = len(aligned_benchmark) > 1
                    try:
                        benchmark_std = aligned_benchmark.std() if len(aligned_benchmark) > 0 else 0
                        has_positive_std = benchmark_std > 0 if pd.notna(benchmark_std) else False
                    except Exception:
                        has_positive_std = False
                    is_numeric = pd.api.types.is_numeric_dtype(aligned_benchmark)
                    
                    if has_sufficient_data and has_positive_std and is_numeric:
                        fig = pf.create_returns_tear_sheet(
                            returns=aligned_returns,
                            benchmark_rets=aligned_benchmark,
                            return_fig=True,
                            live_start_date=None
                        )
                    else:
                        # Fall back to no benchmark if benchmark data is insufficient
                        fig = pf.create_returns_tear_sheet(
                            returns=clean_returns,
                            return_fig=True,
                            live_start_date=None
                        )
                else:
                    fig = pf.create_returns_tear_sheet(
                        returns=clean_returns,
                        return_fig=True,
                        live_start_date=None
                    )
                
                # Save the tear sheet
                fig.savefig(chart_file, dpi=300, bbox_inches='tight', format='png')
                plt.close(fig)
                
                logger.info(f"Pyfolio tear sheet saved to {chart_file}")
                logger.info(f"Performance statistics saved to {stats_file}")
                
                return str(chart_file)
                
            except Exception as e:
                error_msg = str(e).lower()
                logger.warning(f"Failed to create pyfolio tear sheet: {e}. Falling back to custom charts.")
                # Fallback to custom charts if pyfolio tear sheet fails
                return self._create_fallback_charts(returns, chart_file, portfolio_series, benchmark_data)
            
        except Exception as e:
            logger.error(f"Failed to generate pyfolio charts: {e}")
            return None
    
    def _fetch_benchmark_data(self, start_date: str, end_date: str, returns_index: pd.DatetimeIndex) -> Optional[dict]:
        """Fetch benchmark data (SPY and QQQ) for comparison.
        
        Args:
            start_date: Start date for benchmark data
            end_date: End date for benchmark data
            returns_index: DatetimeIndex to align benchmark data with
            
        Returns:
            Dictionary with benchmark returns series or None if failed
        """
        try:
            logger.info("Fetching benchmark data (SPY, QQQ)...")
            
            # Fetch SPY and QQQ data
            benchmarks = ['SPY', 'QQQ']
            benchmark_data = {}
            
            for symbol in benchmarks:
                try:
                    data = yf.download(symbol, start=start_date, end=end_date, progress=False)
                    if not data.empty:
                        # Calculate returns
                        prices = data['Adj Close'] if 'Adj Close' in data.columns else data['Close']
                        returns = prices.pct_change().dropna()
                        # Align with returns index
                        aligned_returns = returns.reindex(returns_index, method='ffill').dropna()
                        benchmark_data[symbol] = aligned_returns
                        logger.info(f"Successfully fetched {symbol} data: {len(aligned_returns)} returns")
                    else:
                        logger.warning(f"No data retrieved for {symbol}")
                except Exception as e:
                    logger.warning(f"Failed to fetch {symbol} data: {e}")
            
            if not benchmark_data:
                logger.warning("No benchmark data available")
                return None
            
            logger.info(f"Benchmark data fetched: {list(benchmark_data.keys())}")
            return benchmark_data
            
        except Exception as e:
            logger.error(f"Error fetching benchmark data: {e}")
            return None
    
    def _prepare_series_for_pyfolio(self, series: pd.Series, series_name: str = "returns") -> pd.Series:
        """Prepare a pandas Series for pyfolio compatibility.
        
        This function addresses the pandas Series boolean ambiguity issue that occurs
        in newer pandas versions when pyfolio tries to evaluate Series in boolean contexts.
        
        Args:
            series: Input pandas Series
            series_name: Name to assign to the Series
            
        Returns:
            Properly formatted Series for pyfolio
        """
        # Clean the data
        clean_series = series.dropna()
        clean_series = clean_series.replace([np.inf, -np.inf], np.nan).dropna()
        
        # Ensure proper data type
        clean_series = clean_series.astype(np.float64)
        
        # Set series name (pyfolio expects named Series)
        clean_series.name = series_name
        
        # Ensure proper DatetimeIndex
        if not isinstance(clean_series.index, pd.DatetimeIndex):
            clean_series.index = pd.to_datetime(clean_series.index)
        
        # Infer frequency if not set
        if hasattr(clean_series.index, 'freq') and clean_series.index.freq is None:
            try:
                clean_series.index.freq = pd.infer_freq(clean_series.index)
            except (ValueError, TypeError):
                # If frequency inference fails, continue without setting it
                pass
        
        return clean_series
    
    def _safe_pyfolio_stats(self, series: pd.Series, series_name: str = "returns") -> Optional[str]:
        """Safely generate pyfolio performance statistics.
        
        Args:
            series: Returns series
            series_name: Name for the series
            
        Returns:
            String representation of pyfolio performance statistics or None if failed
        """
        try:
            # Prepare series for pyfolio
            clean_series = self._prepare_series_for_pyfolio(series, series_name)
            
            # Validate that we have sufficient data
            # Use explicit boolean conversion to avoid pandas Series ambiguity
            has_insufficient_data = len(clean_series) < 2
            try:
                series_std = clean_series.std() if len(clean_series) > 0 else 0
                has_zero_std = series_std == 0 if pd.notna(series_std) else True
            except Exception:
                has_zero_std = True
            
            if has_insufficient_data or has_zero_std:
                std_value = float(clean_series.std()) if len(clean_series) > 0 else 0.0
                raise ValueError(f"Insufficient or invalid data for pyfolio analysis: {len(clean_series)} points, std={std_value:.6f}")
            
            # Try different pyfolio function calls based on available API
            stats = None
            try:
                # Try importing timeseries module directly
                import pyfolio.timeseries as ts
                if hasattr(ts, 'perf_stats'):
                    stats = ts.perf_stats(clean_series)
                else:
                    logger.warning(f"pyfolio.timeseries module found but no perf_stats function for {series_name}")
                    return None
            except ImportError:
                # Fallback to checking main pyfolio module
                if hasattr(pf, 'timeseries') and hasattr(pf.timeseries, 'perf_stats'):
                    stats = pf.timeseries.perf_stats(clean_series)
                elif hasattr(pf, 'perf_stats'):
                    stats = pf.perf_stats(clean_series)
                else:
                    logger.info(f"pyfolio-reloaded {getattr(pf, '__version__', 'unknown')} doesn't have expected API structure for {series_name}. Using custom calculations.")
                    return None
                
            return str(stats)
            
        except Exception as e:
            # Check for specific pandas boolean ambiguity error
            error_msg = str(e).lower()
            if "truth value of a series is ambiguous" in error_msg:
                logger.warning(f"Pyfolio pandas compatibility issue for {series_name}: {e}. Using custom calculations.")
            elif "ninf" in error_msg and "numpy 2.0" in error_msg:
                logger.warning(f"NumPy 2.0 compatibility issue in pyfolio for {series_name}: {e}. Using custom calculations.")
            elif "arg must be a list, tuple, 1-d array, or series" in error_msg:
                logger.warning(f"Pyfolio data format issue for {series_name}: {e}. Using custom calculations.")
            else:
                logger.warning(f"Pyfolio stats generation failed for {series_name}: {e}. Using custom calculations.")
            return None

    def _generate_performance_stats(self, returns: pd.Series, benchmark_returns: Optional[pd.Series], 
                                   output_path: Path, timestamp: str) -> str:
        """Generate comprehensive performance statistics using pyfolio.
        
        Args:
            returns: Strategy returns
            benchmark_returns: Benchmark returns (optional)
            output_path: Directory to save statistics
            timestamp: Timestamp for filename
            
        Returns:
            Path to saved statistics file
        """
        stats_file = output_path / f"backtest_stats_{timestamp}.txt"
        
        try:
            # Try to generate pyfolio performance statistics
            perf_stats = self._safe_pyfolio_stats(returns, "strategy_returns")
            pyfolio_stats_available = perf_stats is not None
            
            with open(stats_file, 'w') as f:
                f.write("=" * 60 + "\n")
                f.write("BACKTEST PERFORMANCE STATISTICS\n")
                f.write("=" * 60 + "\n\n")
                
                f.write("Strategy Performance:\n")
                f.write("-" * 30 + "\n")
                
                if pyfolio_stats_available and perf_stats is not None:
                    f.write(str(perf_stats))
                else:
                    # Generate comprehensive custom statistics
                    total_return = (1 + returns).prod() - 1
                    annualized_return = (1 + returns).mean() ** 252 - 1
                    volatility = returns.std() * np.sqrt(252)
                    sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
                    
                    # Calculate max drawdown and related metrics
                    cumulative = (1 + returns).cumprod()
                    running_max = cumulative.expanding().max()
                    drawdown = (cumulative - running_max) / running_max
                    max_drawdown = drawdown.min()
                    
                    # Calmar ratio (annualized return / max drawdown)
                    calmar_ratio = abs(annualized_return / max_drawdown) if max_drawdown != 0 else 0
                    
                    # Sortino ratio (downside deviation)
                    downside_returns = returns[returns < 0]
                    downside_std = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else volatility
                    sortino_ratio = annualized_return / downside_std if downside_std > 0 else 0
                    
                    # Omega ratio (gains vs losses)
                    gains = returns[returns > 0].sum()
                    losses = abs(returns[returns < 0].sum())
                    omega_ratio = gains / losses if losses > 0 else float('inf')
                    
                    # Statistical moments
                    skewness = returns.skew()
                    kurtosis = returns.kurtosis()
                    
                    # Tail ratio (95th percentile / 5th percentile)
                    percentile_95 = returns.quantile(0.95)
                    percentile_5 = returns.quantile(0.05)
                    tail_ratio = abs(percentile_95 / percentile_5) if percentile_5 != 0 else 0
                    
                    # Value at Risk (5% VaR)
                    var_5 = returns.quantile(0.05)
                    
                    # Stability (R-squared of cumulative returns vs time)
                    try:
                        from scipy import stats
                        time_index = np.arange(len(cumulative))
                        slope, intercept, r_value, p_value, std_err = stats.linregress(time_index, cumulative.values)
                        stability = r_value ** 2
                    except ImportError:
                        # Fallback calculation without scipy
                        stability = 0.0
                    
                    # Format output with appropriate decimal places and percentages
                    f.write(f"Annual return           {annualized_return:.6f}\n")
                    f.write(f"Cumulative returns      {total_return:.6f}\n")
                    f.write(f"Annual volatility       {volatility:.6f}\n")
                    f.write(f"Sharpe ratio            {sharpe_ratio:.6f}\n")
                    f.write(f"Calmar ratio            {calmar_ratio:.6f}\n")
                    f.write(f"Stability               {stability:.6f}\n")
                    f.write(f"Max drawdown           {max_drawdown:.6f}\n")
                    f.write(f"Omega ratio             {omega_ratio:.6f}\n")
                    f.write(f"Sortino ratio           {sortino_ratio:.6f}\n")
                    f.write(f"Skew                    {skewness:.6f}\n")
                    f.write(f"Kurtosis               {kurtosis:.6f}\n")
                    f.write(f"Tail ratio              {tail_ratio:.6f}\n")
                    f.write(f"Daily value at risk    {var_5:.6f}\n")
                    f.write(f"\n")
                    f.write(f"Additional Metrics (Percentage Format):\n")
                    f.write(f"Total Return: {total_return:.2%}\n")
                    f.write(f"Annualized Return: {annualized_return:.2%}\n")
                    f.write(f"Volatility: {volatility:.2%}\n")
                    f.write(f"Max Drawdown: {max_drawdown:.2%}\n")
                    f.write(f"Daily VaR (5%): {var_5:.2%}\n")
                    f.write(f"Number of Returns: {len(returns)}\n")
                
                f.write("\n\n")
                
                # Add benchmark comparison if available
                if benchmark_returns is not None and not benchmark_returns.empty:
                    try:
                        # Safely align returns for comparison
                        try:
                            aligned_strategy, aligned_benchmark = returns.align(benchmark_returns, join='inner')
                            aligned_benchmark = aligned_benchmark.dropna()
                        except Exception as align_error:
                            if "truth value of a series is ambiguous" in str(align_error).lower():
                                logger.warning(f"Pandas Series boolean ambiguity during alignment. Using fallback method.")
                                # Fallback: use common index
                                common_index = returns.index.intersection(benchmark_returns.index)
                                aligned_strategy = returns.loc[common_index]
                                aligned_benchmark = benchmark_returns.loc[common_index].dropna()
                            else:
                                raise align_error

                        # Robust validation of aligned_benchmark after dropna() with safe boolean evaluation
                        has_data = len(aligned_benchmark) > 0
                        
                        if has_data:
                            # Use explicit boolean conversion to avoid pandas Series ambiguity
                            try:
                                has_no_all_nan = not bool(aligned_benchmark.isna().all())
                            except ValueError:
                                # Handle "truth value of Series is ambiguous" error
                                has_no_all_nan = not aligned_benchmark.isna().all().all() if hasattr(aligned_benchmark.isna().all(), 'all') else not aligned_benchmark.isna().all()
                            
                            try:
                                has_non_zero_values = not bool((aligned_benchmark == 0).all())
                            except ValueError:
                                # Handle "truth value of Series is ambiguous" error  
                                has_non_zero_values = not (aligned_benchmark == 0).all().all() if hasattr((aligned_benchmark == 0).all(), 'all') else not (aligned_benchmark == 0).all()
                            
                            is_numeric = pd.api.types.is_numeric_dtype(aligned_benchmark)
                        else:
                            has_no_all_nan = False
                            has_non_zero_values = False
                            is_numeric = False
                        
                        benchmark_is_valid = has_data and has_no_all_nan and has_non_zero_values and is_numeric
                        
                        if benchmark_is_valid:
                            f.write("Benchmark Performance (SPY):\n")
                            f.write("-" * 30 + "\n")

                            if pyfolio_stats_available:
                                # Try to generate pyfolio benchmark statistics
                                benchmark_stats = self._safe_pyfolio_stats(aligned_benchmark, "benchmark_returns")
                                
                                if benchmark_stats is not None:
                                    f.write(str(benchmark_stats))
                                else:
                                    # Custom benchmark calculations with safe operations
                                    bench_total_return = self._safe_total_return(aligned_benchmark)
                                    bench_annualized_return = self._safe_annualized_return(aligned_benchmark)
                                    bench_volatility = self._safe_volatility(aligned_benchmark)
                                    bench_sharpe = bench_annualized_return / bench_volatility if bench_volatility > 0 else 0

                                    f.write(f"Total Return: {bench_total_return:.2%}\n")
                                    f.write(f"Annualized Return: {bench_annualized_return:.2%}\n")
                                    f.write(f"Volatility: {bench_volatility:.2%}\n")
                                    f.write(f"Sharpe Ratio: {bench_sharpe:.2f}\n")
                            else:
                                # Custom benchmark calculations with safe operations
                                bench_total_return = self._safe_total_return(aligned_benchmark)
                                bench_annualized_return = self._safe_annualized_return(aligned_benchmark)
                                bench_volatility = self._safe_volatility(aligned_benchmark)
                                bench_sharpe = bench_annualized_return / bench_volatility if bench_volatility > 0 else 0

                                f.write(f"Total Return: {bench_total_return:.2%}\n")
                                f.write(f"Annualized Return: {bench_annualized_return:.2%}\n")
                                f.write(f"Volatility: {bench_volatility:.2%}\n")
                                f.write(f"Sharpe Ratio: {bench_sharpe:.2f}\n")
                            
                            f.write("\n\n")
                            
                            # Calculate relative performance with safe operations
                            strategy_total_return = self._safe_total_return(aligned_strategy)
                            benchmark_total_return = self._safe_total_return(aligned_benchmark)
                            excess_return = strategy_total_return - benchmark_total_return
                            
                            f.write("Relative Performance:\n")
                            f.write("-" * 30 + "\n")
                            f.write(f"Strategy Total Return: {strategy_total_return:.2%}\n")
                            f.write(f"Benchmark Total Return: {benchmark_total_return:.2%}\n")
                            f.write(f"Excess Return: {excess_return:.2%}\n")
                            
                    except Exception as e:
                        error_msg = str(e).lower()
                        if "truth value of a series is ambiguous" in error_msg:
                            logger.warning(f"Failed to generate benchmark comparison due to pandas Series boolean ambiguity: {e}")
                        else:
                            logger.warning(f"Failed to generate benchmark comparison: {e}")
                        f.write("Benchmark comparison failed\n")
                 
                f.write("\n" + "=" * 60 + "\n")
                f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                if not pyfolio_stats_available:
                    f.write("Note: Custom statistics used due to pyfolio compatibility issues\n")
            
            logger.info(f"Performance statistics saved to {stats_file}")
            return str(stats_file)
            
        except Exception as e:
            logger.error(f"Failed to generate performance statistics: {e}")
            # Create a basic stats file
            with open(stats_file, 'w') as f:
                f.write("Performance statistics generation failed\n")
                f.write(f"Error: {str(e)}\n")
            return str(stats_file)
    
    def _safe_total_return(self, returns: pd.Series) -> float:
        """Safely calculate total return avoiding pandas Series boolean ambiguity.
        
        Args:
            returns: Returns series
            
        Returns:
            Total return as float
        """
        try:
            # Use numpy array to avoid pandas Series boolean issues
            return np.prod(1 + returns.values) - 1
        except Exception:
            # Fallback to iterative calculation
            total = 1.0
            for ret in returns:
                total *= (1 + ret)
            return total - 1
    
    def _safe_annualized_return(self, returns: pd.Series) -> float:
        """Safely calculate annualized return.
        
        Args:
            returns: Returns series
            
        Returns:
            Annualized return as float
        """
        try:
            return (1 + returns.mean()) ** 252 - 1
        except Exception:
            return 0.0
    
    def _safe_volatility(self, returns: pd.Series) -> float:
        """Safely calculate volatility.
        
        Args:
            returns: Returns series
            
        Returns:
            Annualized volatility as float
        """
        try:
            return returns.std() * np.sqrt(252)
        except Exception:
            return 0.0

    def _create_fallback_charts(self, returns: pd.Series, chart_file: Path, portfolio_series: pd.Series, benchmark_data: Optional[dict] = None) -> str:
        """Create fallback charts if pyfolio tear sheet fails.
        
        Args:
            returns: Strategy returns
            chart_file: Path to save chart
            portfolio_series: Portfolio value series
            benchmark_data: Dictionary containing benchmark returns (SPY, QQQ)
            
        Returns:
            Path to saved chart file
        """
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Backtest Performance Analysis (Fallback)', fontsize=16)
            
            # Plot 1: Cumulative returns with benchmarks
            cumulative_returns = (1 + returns).cumprod()
            axes[0, 0].plot(cumulative_returns.index, (cumulative_returns - 1) * 100, 
                           label='Strategy', linewidth=2, color='blue')
            
            # Add benchmark comparisons if available
            if benchmark_data:
                for symbol, bench_returns in benchmark_data.items():
                    if len(bench_returns) > 0:
                        # Align benchmark with strategy returns
                        aligned_bench = bench_returns.reindex(returns.index, method='ffill').fillna(0)
                        bench_cumulative = (1 + aligned_bench).cumprod()
                        color = 'red' if symbol == 'SPY' else 'green'
                        axes[0, 0].plot(bench_cumulative.index, (bench_cumulative - 1) * 100, 
                                       label=symbol, linewidth=1.5, color=color, alpha=0.8)
            
            axes[0, 0].set_title('Cumulative Returns Comparison (%)')
            axes[0, 0].set_ylabel('Returns (%)')
            axes[0, 0].legend()
            axes[0, 0].grid(True)
            
            # Plot 2: Rolling Sharpe ratio
            if len(returns) > 30:
                rolling_sharpe = returns.rolling(window=30).mean() / returns.rolling(window=30).std() * np.sqrt(252)
                axes[0, 1].plot(rolling_sharpe.index, rolling_sharpe)
                axes[0, 1].set_title('Rolling 30-Day Sharpe Ratio')
                axes[0, 1].set_ylabel('Sharpe Ratio')
                axes[0, 1].grid(True)
            else:
                axes[0, 1].text(0.5, 0.5, 'Insufficient data\nfor rolling Sharpe', 
                               ha='center', va='center', transform=axes[0, 1].transAxes)
                axes[0, 1].set_title('Rolling Sharpe Ratio')
            
            # Plot 3: Drawdown
            peak = portfolio_series.expanding().max()
            drawdown = (portfolio_series - peak) / peak * 100
            axes[1, 0].fill_between(drawdown.index, drawdown, 0, alpha=0.3, color='red')
            axes[1, 0].plot(drawdown.index, drawdown, color='red')
            axes[1, 0].set_title('Drawdown (%)')
            axes[1, 0].set_ylabel('Drawdown (%)')
            axes[1, 0].grid(True)
            
            # Plot 4: Return distribution
            axes[1, 1].hist(returns * 100, bins=50, alpha=0.7, edgecolor='black')
            axes[1, 1].set_title('Daily Returns Distribution (%)')
            axes[1, 1].set_xlabel('Returns (%)')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].grid(True)
            
            plt.tight_layout()
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Fallback charts saved to {chart_file}")
            return str(chart_file)
            
        except Exception as e:
            logger.error(f"Failed to create fallback charts: {e}")
            return str(chart_file)
    
    def _save_results(self, results: Dict[str, Any], output_dir: str):
        """Save backtest results to files."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate timestamp for consistent file naming
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save main results as JSON
        results_file = output_path / f"backtest_results_{timestamp}.json"
        
        # Create a serializable copy of results
        serializable_results = results.copy()
        
        # Convert numpy arrays to lists for JSON serialization
        if 'portfolio_values' in serializable_results:
            if isinstance(serializable_results['portfolio_values'], np.ndarray):
                serializable_results['portfolio_values'] = serializable_results['portfolio_values'].tolist()
        
        with open(results_file, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        
        logger.info(f"Backtest results saved to {results_file}")
        
        # Save portfolio values as CSV for easy analysis
        if 'portfolio_values' in results:
            portfolio_df = pd.DataFrame({
                'step': range(len(results['portfolio_values'])),
                'portfolio_value': results['portfolio_values']
            })
            
            portfolio_file = output_path / f"portfolio_values_{timestamp}.csv"
            portfolio_df.to_csv(portfolio_file, index=False)
            logger.info(f"Portfolio values saved to {portfolio_file}")
            
        # Generate pyfolio charts if enabled
        if self.settings.backtesting.plot_results:
            start_date = results.get('backtest_config', {}).get('start_date')
            end_date = results.get('backtest_config', {}).get('end_date')
            chart_file = self._generate_pyfolio_charts(results, output_dir, start_date, end_date)
            if chart_file:
                # Add chart file path to results
                serializable_results['chart_file'] = chart_file
                # Re-save results with chart file path
                with open(results_file, 'w') as f:
                    json.dump(serializable_results, f, indent=2, default=str)