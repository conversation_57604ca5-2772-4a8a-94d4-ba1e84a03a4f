#!/usr/bin/env python3
"""
Test to demonstrate the mathematical error in reward calculation.
Shows why avgR values like -0.03, -0.02 are inaccurate.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔍 TESTING REWARD CALCULATION ACCURACY")
print("="*50)

# Create test data with controlled price movements
test_data = []
initial_price = 100.0
daily_return = 0.001  # 0.1% daily return

for i in range(10):  # 10 trading days
    price = initial_price * (1 + daily_return) ** i
    test_data.append({
        'tic': 'TEST',
        'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
        'close': price, 'open': price, 'high': price*1.01, 'low': price*0.99,
        'volume': 1000000,
        'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
        'ema_12': price, 'ema_26': price, 'rsi_14': 50.0,
        'macd_12_26_9': 0.0, 'macds_12_26_9': 0.0, 'macdh_12_26_9': 0.0,
        'cci_20': 0.0, 'adx_14': 30.0, 'dmp_14': 25.0, 'dmn_14': 25.0,
        'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price, 'bbu_20_2.0': price*1.05,
        'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000, 'turbulence': 0.1,
        'price_range': 0.02, 'price_position': 0.5, 'returns_1d': daily_return,
        'returns_5d': 0.05, 'returns_20d': 0.2, 'volume_ma_20': 1000000,
        'volume_ratio': 1.0, 'volatility_20d': 0.02, 'vix_ma_5': 20.0,
        'vix_ma_20': 20.0, 'vix_percentile_252': 0.5, 'vix_change': 0.0,
        'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
    })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📊 Test setup:")
print(f"  - 10 trading days")
print(f"  - Daily return: {daily_return:.3f} ({daily_return*100:.1f}%)")
print(f"  - Expected cumulative return: {(1 + daily_return)**10 - 1:.6f} ({((1 + daily_return)**10 - 1)*100:.3f}%)")

# Create environment
env = AsymmetricTradingEnv(
    df=df,
    stock_dim=1,
    hmax=100,
    initial_amount=100000,
    num_stock_shares=[0],
    buy_cost_pct=[0.0],  # No transaction costs for clear math
    sell_cost_pct=[0.0],
    reward_scaling=1.0,
    asymmetric_config=AsymmetricConfig(symbols=['TEST']),
    log_level='ERROR',
    tech_indicator_list=[
        'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 
        'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
        'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 
        'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 
        'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 
        'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 
        'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
    ]
)

print(f"\n🏃 Running episode with buy-and-hold strategy...")

# Reset environment
state, _ = env.reset()

# Run episode with simple buy-and-hold
# Action 1.0 = buy 100% of available cash
action = np.array([1.0])

step_rewards = []
portfolio_values = []

print(f"\nStep-by-step analysis:")
print(f"{'Step':<5} {'Portfolio':<12} {'Daily Return':<12} {'Step Reward':<12}")
print("-" * 50)

for step in range(9):  # 9 steps (10 days total)
    state, reward, done, truncated, info = env.step(action)
    step_rewards.append(reward)
    portfolio_values.append(info['total_asset'])
    
    daily_return_actual = reward  # This is the daily percentage return
    print(f"{step+1:<5} ${info['total_asset']:<11.2f} {daily_return_actual:<12.6f} {reward:<12.6f}")
    
    if done or truncated:
        break
    
    # After first step, hold position (action = 0)
    action = np.array([0.0])

print(f"\n📈 RESULTS ANALYSIS:")
print(f"{'Metric':<30} {'Value':<15}")
print("-" * 45)

# Current (wrong) calculation - sum of step rewards
sum_of_step_rewards = sum(step_rewards)
print(f"{'Sum of step rewards:':<30} {sum_of_step_rewards:<15.6f}")

# Correct calculation - based on portfolio values
if portfolio_values:
    initial_portfolio = env.initial_amount
    final_portfolio = portfolio_values[-1]
    correct_return = (final_portfolio - initial_portfolio) / initial_portfolio
    print(f"{'Correct episode return:':<30} {correct_return:<15.6f}")
    
    # Show the error
    error = abs(sum_of_step_rewards - correct_return)
    error_pct = (error / correct_return) * 100 if correct_return != 0 else 0
    print(f"{'Mathematical error:':<30} {error:<15.6f}")
    print(f"{'Error percentage:':<30} {error_pct:<15.2f}%")

# Expected vs actual
expected_return = (1 + daily_return)**10 - 1
print(f"{'Expected return (theory):':<30} {expected_return:<15.6f}")

print(f"\n💡 EXPLANATION:")
print(f"The avgR values in ElegantRL training are WRONG because:")
print(f"  1. Environment returns daily percentage returns as step rewards")
print(f"  2. ElegantRL sums these over {1761} steps: Σ(daily returns)")
print(f"  3. Sum of percentage returns ≠ cumulative return")
print(f"  4. Over 1761 days, this error becomes significant")

print(f"\n🔧 THE FIX:")
print(f"Environment should set cumulative_returns attribute for ElegantRL to use")
print(f"instead of summing mathematically incorrect step rewards.")