#!/usr/bin/env python3
"""
Debug evaluation noise manager behavior.
"""

import sys
sys.path.insert(0, 'src')

print("🔧 DEBUGGING EVALUATION NOISE MANAGER")
print("=" * 45)

# Test 1: Check if ElegantRL is available and working
print("✅ Test 1: ElegantRL availability")
try:
    import elegantrl.train.evaluator as evaluator_module
    print(f"   ElegantRL evaluator module: {evaluator_module}")
    print(f"   Original function: {evaluator_module.get_rewards_and_steps}")
    print(f"   Function name: {evaluator_module.get_rewards_and_steps.__name__}")
except ImportError as e:
    print(f"   ❌ ElegantRL not available: {e}")
    exit(1)

# Test 2: Check noise manager state
print("\n✅ Test 2: Noise manager state")
from utils.evaluation_noise import EvaluationNoiseManager

manager = EvaluationNoiseManager.get_instance()
print(f"   Enabled: {manager.enabled}")
print(f"   Applied: {manager._applied}")
print(f"   Original functions stored: {list(manager.original_functions.keys())}")

# Test 3: Enable noise and check monkey-patch
print("\n✅ Test 3: Enable noise and check monkey-patch")
manager.enable(noise_scale=0.05)
print(f"   After enable - Enabled: {manager.enabled}")
print(f"   After enable - Applied: {manager._applied}")
print(f"   After enable - Original functions: {list(manager.original_functions.keys())}")
print(f"   Current function: {evaluator_module.get_rewards_and_steps}")
print(f"   Function name: {evaluator_module.get_rewards_and_steps.__name__}")

# Test 4: Check if the monkey-patch is actually there
print("\n✅ Test 4: Check function properties")
current_func = evaluator_module.get_rewards_and_steps
print(f"   Function type: {type(current_func)}")
print(f"   Function module: {getattr(current_func, '__module__', 'None')}")
print(f"   Is bound method: {hasattr(current_func, '__self__')}")

if hasattr(current_func, '__self__'):
    print(f"   Bound to: {current_func.__self__}")

# Test 5: Manual test of evaluation
print("\n✅ Test 5: Manual evaluation test")
import numpy as np
import pandas as pd
import torch as th
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

# Create minimal test data
test_data = []
for day in range(5):
    for symbol in ['AAPL']:
        test_data.append({
            'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
            'close': 150, 'open': 150, 'high': 151, 'low': 149,
            'volume': 1000000, 'day': day,
            'sma_5': 150, 'rsi_14': 50, 'turbulence': 0.1
        })

df = pd.DataFrame(test_data).set_index('day')

# Create environment with noise enabled
env = AsymmetricTradingEnv(
    df=df, stock_dim=1, hmax=100, initial_amount=100000,
    num_stock_shares=[0], buy_cost_pct=[0.001], sell_cost_pct=[0.001],
    asymmetric_config=AsymmetricConfig(symbols=['AAPL']),
    log_level='ERROR', tech_indicator_list=['sma_5', 'rsi_14', 'turbulence'],
    evaluation_noise_scale=0.05  # Enable noise
)

class TestActor(th.nn.Module):
    def __init__(self):
        super().__init__()
        self.dummy = th.nn.Parameter(th.tensor([0.0]))
    def forward(self, state):
        return th.tensor([[0.1]], dtype=th.float32)

actor = TestActor()

print(f"   Environment created with noise_scale=0.05")
print(f"   Noise manager enabled: {EvaluationNoiseManager.is_enabled()}")

# Test direct evaluation
results = []
for i in range(3):
    result, steps = evaluator_module.get_rewards_and_steps(env, actor)
    results.append(result)
    print(f"   Evaluation {i+1}: {result:.8f}")

std_result = np.std(results)
print(f"   Standard deviation: {std_result:.12f}")
print(f"   Results: {results}")
print(f"   Variation achieved: {std_result > 1e-10}")

if std_result > 0:
    print("   ✅ SUCCESS: Evaluation noise is working!")
else:
    print("   ❌ ISSUE: Still no variation in evaluation results")
    
    # Debug the evaluation function call
    print("\n🔍 Debug: Manual function call")
    manager_instance = EvaluationNoiseManager.get_instance()
    print(f"   Manager enabled: {manager_instance.enabled}")
    
    # Try calling the enhanced evaluation directly
    try:
        result1 = manager_instance._enhanced_evaluation(env, actor)
        result2 = manager_instance._enhanced_evaluation(env, actor)
        print(f"   Direct call 1: {result1}")
        print(f"   Direct call 2: {result2}")
        print(f"   Direct variation: {abs(result1[0] - result2[0]) > 0}")
    except Exception as e:
        print(f"   Direct call error: {e}")