#!/usr/bin/env python3
"""
Debug multiprocessing monkey-patch issue.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔍 DEBUGGING MULTIPROCESSING MONKEY-PATCH")
print("="*50)

# The issue is likely that the monkey-patch is applied in the main process
# but ElegantRL's multiprocessing workers start fresh and don't have the patch

print("1. Problem analysis:")
print("   - Monkey-patch works in single-process test ✅")
print("   - Training uses multiprocessing workers ❌") 
print("   - Workers start fresh without monkey-patch")

print("\n2. Solutions to try:")

print("\n   Solution A: Apply patch in worker initialization")
print("   - Modify how ElegantRL workers are started")
print("   - Ensure patch is applied in each worker process")

print("\n   Solution B: Increase noise scale significantly") 
print("   - Make noise large enough to show up in training display")
print("   - Current noise might be too small for 2-decimal precision")

print("\n   Solution C: Add noise directly in environment")
print("   - Modify environment to add noise internally")
print("   - Doesn't rely on external monkey-patching")

# Test current noise scale
test_data = []
for i in range(5):
    price = 100 + i
    test_data.append({
        'tic': 'TEST', 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
        'close': price, 'open': price, 'high': price*1.01, 'low': price*0.99,
        'volume': 1000000, 'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
        'ema_12': price, 'ema_26': price, 'rsi_14': 50.0, 'macd_12_26_9': 0.0,
        'macds_12_26_9': 0.0, 'macdh_12_26_9': 0.0, 'cci_20': 0.0, 'adx_14': 30.0,
        'dmp_14': 25.0, 'dmn_14': 25.0, 'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price,
        'bbu_20_2.0': price*1.05, 'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000,
        'turbulence': 0.1, 'price_range': 0.02, 'price_position': 0.5, 'returns_1d': 0.002,
        'returns_5d': 0.01, 'returns_20d': 0.04, 'volume_ma_20': 1000000, 'volume_ratio': 1.0,
        'volatility_20d': 0.02, 'vix_ma_5': 20.0, 'vix_ma_20': 20.0, 'vix_percentile_252': 0.5,
        'vix_change': 0.0, 'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
    })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

env = AsymmetricTradingEnv(
    df=df, stock_dim=1, hmax=100, initial_amount=100000,
    num_stock_shares=[0], buy_cost_pct=[0.0], sell_cost_pct=[0.0],
    reward_scaling=1e-4, asymmetric_config=AsymmetricConfig(symbols=['TEST']),
    log_level='ERROR',
    tech_indicator_list=[
        'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 
        'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
        'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 
        'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 
        'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 
        'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 
        'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
    ]
)

# Run episode to get base return
state, _ = env.reset()
action = np.array([1.0])  # Buy all
for _ in range(4):
    state, reward, done, truncated, info = env.step(action)
    action = np.array([0.0])  # Hold
    if done or truncated:
        break

base_return = getattr(env, 'cumulative_returns', 0)
unscaled_return = base_return / env.reward_scaling

print(f"\n3. Current return analysis:")
print(f"   Base cumulative_returns: {base_return:.8f}")
print(f"   Unscaled return: {unscaled_return:.6f} ({unscaled_return*100:.3f}%)")
print(f"   Training shows avgR: 20.21")
print(f"   Expected unscaled: {20.21 / 1e-4:.1f} = {20.21 / 1e-4:.0f}%")

print(f"\n4. Noise scale analysis:")
print(f"   Current noise scale: 0.02 (2%)")
print(f"   Noise on 20.21: ±{20.21 * 0.02:.3f}")
print(f"   But training shows exactly 20.21 → noise not applied")

print(f"\n5. IMMEDIATE FIX: Add noise directly to environment")
print(f"   This bypasses multiprocessing issues")
print(f"   Environment adds randomness to cumulative_returns calculation")