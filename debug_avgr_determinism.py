#!/usr/bin/env python3
"""
Debug why avgR stays constant despite noise injection.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import torch

# Test the monkey-patch mechanism
print("🔍 DEBUGGING AVGR DETERMINISM")
print("="*50)

# Check if the monkey-patch is working
print("1. Testing monkey-patch mechanism...")

# Import ElegantRL evaluator
sys.path.append('/app/workspaces/ElegantRL')
try:
    import elegantrl.train.evaluator as evaluator_module
    original_func = evaluator_module.get_rewards_and_steps
    print(f"   Original function: {original_func}")
    print(f"   Function location: {original_func.__module__}")
    
    # Apply our monkey-patch
    from models.fix_elegantrl_evaluator import get_rewards_and_steps_with_noise
    
    def get_rewards_and_steps_with_eval_noise(env, actor, if_render=False):
        print(f"   🎯 MONKEY-PATCH CALLED!")
        result = get_rewards_and_steps_with_noise(env, actor, noise_scale=0.1, if_render=if_render)  # Large noise for testing
        print(f"   🎯 MONKEY-PATCH RESULT: {result[0]:.8f}")
        return result
    
    # Replace the function
    evaluator_module.get_rewards_and_steps = get_rewards_and_steps_with_eval_noise
    patched_func = evaluator_module.get_rewards_and_steps
    print(f"   Patched function: {patched_func}")
    print(f"   Patch successful: {patched_func != original_func}")
    
except Exception as e:
    print(f"   ❌ Error setting up monkey-patch: {e}")

# Create test environment and actor
print("\n2. Creating test environment...")
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

# Simple test data
test_data = []
for i in range(20):
    price = 100 + i * 0.5
    test_data.append({
        'tic': 'TEST', 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
        'close': price, 'open': price, 'high': price*1.01, 'low': price*0.99,
        'volume': 1000000, 'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
        'ema_12': price, 'ema_26': price, 'rsi_14': 50.0, 'macd_12_26_9': 0.0,
        'macds_12_26_9': 0.0, 'macdh_12_26_9': 0.0, 'cci_20': 0.0, 'adx_14': 30.0,
        'dmp_14': 25.0, 'dmn_14': 25.0, 'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price,
        'bbu_20_2.0': price*1.05, 'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000,
        'turbulence': 0.1, 'price_range': 0.02, 'price_position': 0.5, 'returns_1d': 0.0025,
        'returns_5d': 0.0125, 'returns_20d': 0.05, 'volume_ma_20': 1000000, 'volume_ratio': 1.0,
        'volatility_20d': 0.02, 'vix_ma_5': 20.0, 'vix_ma_20': 20.0, 'vix_percentile_252': 0.5,
        'vix_change': 0.0, 'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
    })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

env = AsymmetricTradingEnv(
    df=df, stock_dim=1, hmax=100, initial_amount=100000,
    num_stock_shares=[0], buy_cost_pct=[0.001], sell_cost_pct=[0.001],
    reward_scaling=1e-4, asymmetric_config=AsymmetricConfig(symbols=['TEST']),
    log_level='ERROR',
    tech_indicator_list=[
        'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 
        'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
        'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 
        'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 
        'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 
        'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 
        'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
    ]
)

# Simple deterministic actor
class TestActor(torch.nn.Module):
    def __init__(self, state_dim, action_dim):
        super().__init__()
        self.dummy = torch.nn.Parameter(torch.tensor([0.0]))
        
    def forward(self, state):
        return torch.tensor([[0.8]], dtype=torch.float32)  # Always buy 80%

actor = TestActor(env.observation_space.shape[0], env.action_space.shape[0])

print("3. Testing evaluation with monkey-patch...")

# Test multiple evaluations (like ElegantRL does)
results = []
for i in range(5):
    print(f"\n   Evaluation {i+1}:")
    
    try:
        # Use the patched function directly
        cumulative_return, steps = evaluator_module.get_rewards_and_steps(env, actor)
        results.append(cumulative_return)
        
        # Check environment's cumulative_returns
        env_return = getattr(env, 'cumulative_returns', None)
        print(f"     ElegantRL result: {cumulative_return:.8f}")
        print(f"     Env cumulative_returns: {env_return:.8f}")
        print(f"     Steps: {steps}")
        
    except Exception as e:
        print(f"     ❌ Error: {e}")
        import traceback
        traceback.print_exc()

print(f"\n4. Results analysis:")
print(f"   Results: {[f'{r:.8f}' for r in results]}")
print(f"   Mean: {np.mean(results):.8f}")
print(f"   Std: {np.std(results):.8f}")
print(f"   Unique values: {len(set([round(r, 8) for r in results]))}")

if np.std(results) > 1e-6:
    print(f"   ✅ SUCCESS: Variation detected! stdR should now be > 0")
else:
    print(f"   ❌ FAILED: Still deterministic. Investigating further...")
    
    print(f"\n5. Deep debugging...")
    
    # Check if noise function is actually called
    print("   Testing noise function directly:")
    test_return1, _ = get_rewards_and_steps_with_noise(env, actor, noise_scale=0.2)
    test_return2, _ = get_rewards_and_steps_with_noise(env, actor, noise_scale=0.2)
    print(f"     Direct call 1: {test_return1:.8f}")
    print(f"     Direct call 2: {test_return2:.8f}")
    print(f"     Direct variation: {abs(test_return1 - test_return2):.8f}")
    
    # Check random number generation
    print("   Testing random number generation:")
    for i in range(3):
        noise = np.random.normal(0, 0.1)
        print(f"     Random {i+1}: {noise:.8f}")

print(f"\n🎯 CONCLUSION:")
if np.std(results) > 1e-6:
    print("   The monkey-patch is working and adding variation!")
    print("   Issue might be in multiprocessing training environment.")
else:
    print("   The monkey-patch is not working properly.")
    print("   Need to investigate why noise isn't being applied.")