#!/usr/bin/env python3
"""Check the already processed data files"""

import pandas as pd
import os

print("🔍 Looking for processed data files")

# Check for processed data files in cache
cache_dir = 'data/cache'
files = os.listdir(cache_dir)

print(f"Found {len(files)} files in cache")

# Look for files that might be processed data (larger files with multiple stocks)
processed_candidates = []

for filename in files:
    if filename.endswith('.csv'):
        filepath = f'{cache_dir}/{filename}'
        try:
            # Just check file size and basic info
            file_size = os.path.getsize(filepath)
            if file_size > 1000000:  # > 1MB, likely processed data
                df = pd.read_csv(filepath, nrows=5)  # Just peek at first 5 rows
                print(f"\n📄 {filename} ({file_size//1024}KB)")
                print(f"  Columns: {list(df.columns)}")
                print(f"  Sample data:")
                print(f"    {df.iloc[0].to_dict()}")
                
                # Load full file if it looks like processed stock data
                if 'tic' in df.columns and 'date' in df.columns:
                    full_df = pd.read_csv(filepath)
                    print(f"  📊 FULL DATA:")
                    print(f"    Shape: {full_df.shape}")
                    print(f"    Unique dates: {full_df['date'].nunique()}")
                    print(f"    Unique symbols: {full_df['tic'].nunique()}")
                    print(f"    Date range: {full_df['date'].min()} to {full_df['date'].max()}")
                    
                    processed_candidates.append((filename, full_df))
                    
                    # Check if this has the expected symbols
                    symbols = full_df['tic'].unique()
                    expected_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'TSLA', 'AVGO', 'ADBE', 'ASML']
                    if len(set(symbols) & set(expected_symbols)) >= 8:  # At least 8 of our symbols
                        print(f"    ⭐ THIS LOOKS LIKE OUR PROCESSED TRAINING DATA!")
                        
        except Exception as e:
            print(f"  Error reading {filename}: {e}")

# Now test the data transformation on the most likely candidate
if processed_candidates:
    print(f"\n🧪 TESTING DATA TRANSFORMATION on best candidate...")
    filename, df = processed_candidates[0]  # Use the first (likely only) candidate
    
    print(f"Using: {filename}")
    
    # Apply same filtering as main.py
    df['date'] = pd.to_datetime(df['date'])
    train_df = df[
        (df['date'] >= '2016-01-01') & 
        (df['date'] <= '2022-12-31')
    ].reset_index(drop=True)
    
    print(f"After 2016-2022 filtering:")
    print(f"  Shape: {train_df.shape}")
    print(f"  Unique dates: {train_df['date'].nunique()}")
    
    # Apply factorize transformation
    train_df = train_df.sort_values(['date', 'tic']).reset_index(drop=True)
    print(f"Before factorize: {train_df['date'].nunique()} unique dates")
    
    train_df['day'] = train_df['date'].factorize()[0]
    print(f"After factorize: {train_df['day'].nunique()} unique day values")
    print(f"Day range: {train_df['day'].min()} to {train_df['day'].max()}")
    
    # Set index
    train_df_indexed = train_df.set_index('day')
    print(f"After set_index: {len(train_df_indexed.index.unique())} unique index values")
    
    # THIS IS THE KEY TEST - what does len(df.index.unique()) return?
    index_unique_count = len(train_df_indexed.index.unique())
    print(f"\n🎯 CRITICAL: len(df.index.unique()) = {index_unique_count}")
    print(f"Environment will terminate episodes at day {index_unique_count - 1}")
    
    if index_unique_count <= 124:
        print("❌ FOUND THE BUG! Data is truncated to ~124 days")
    else:
        print("✅ Data looks good - issue must be elsewhere")

else:
    print("❌ No processed data files found!")