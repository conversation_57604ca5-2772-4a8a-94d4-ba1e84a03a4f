#!/usr/bin/env python3
"""
Simple test to check if the AsymmetricTradingEnv can be imported.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_import():
    """Test basic import of AsymmetricTradingEnv."""
    
    print("Testing basic import...")
    
    try:
        print("1. Testing FinRL import...")
        from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv
        print("✅ FinRL import successful")
        
        print("2. Testing AsymmetricConfig import...")
        from strategies.asymmetric_strategy import AsymmetricConfig
        print("✅ AsymmetricConfig import successful")
        
        print("3. Testing AsymmetricTradingEnv import...")
        from trading.asymmetric_env import AsymmetricTradingEnv
        print("✅ AsymmetricTradingEnv import successful")
        
        print("4. Testing class instantiation (without data)...")
        # Just check if the class can be referenced
        env_class = AsymmetricTradingEnv
        print(f"✅ Class reference successful: {env_class}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_import()
    if success:
        print("\n🎉 All imports successful!")
        sys.exit(0)
    else:
        print("\n💥 Import test failed!")
        sys.exit(1)
