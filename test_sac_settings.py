#!/usr/bin/env python3
"""Test script to verify SAC agent gets correct settings"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from models.sac_agent import SACAgent
import os

print("=== Environment Variable ===")
print(f"SAC_TOTAL_TIMESTEPS: {os.getenv('SAC_TOTAL_TIMESTEPS')}")

print("\n=== SACAgent Configuration ===")
# Create a minimal agent to check its configuration
agent = SACAgent(state_dim=10, action_dim=5)
print(f"agent.sac_config['total_timesteps']: {agent.sac_config['total_timesteps']}")

print("\n=== Expected value: 10000 ===")