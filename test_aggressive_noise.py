#!/usr/bin/env python3
"""
Test aggressive noise scale to see if it creates visible avgR variation.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import torch as th
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔧 TESTING AGGRESSIVE NOISE SCALE FOR AVGR VARIATION")
print("=" * 60)

# Create test data
test_data = []
for day in range(15):
    for symbol in ['AAPL']:
        test_data.append({
            'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
            'close': 150, 'open': 150, 'high': 151, 'low': 149,
            'volume': 1000000, 'day': day,
            'sma_5': 150, 'rsi_14': 50, 'turbulence': 0.1
        })

df = pd.DataFrame(test_data).set_index('day')

# Create environment with aggressive noise scale
env = AsymmetricTradingEnv(
    df=df, stock_dim=1, hmax=100, initial_amount=100000,
    num_stock_shares=[0], buy_cost_pct=[0.001], sell_cost_pct=[0.001],
    asymmetric_config=AsymmetricConfig(symbols=['AAPL']),
    log_level='ERROR', tech_indicator_list=['sma_5', 'rsi_14', 'turbulence'],
    evaluation_noise_scale=0.5  # AGGRESSIVE noise scale
)

class TestActor(th.nn.Module):
    def __init__(self):
        super().__init__()
        self.dummy = th.nn.Parameter(th.tensor([0.0]))
    def forward(self, state):
        return th.tensor([[0.1]], dtype=th.float32)

actor = TestActor()

print(f"Created environment with evaluation_noise_scale=0.5")

# Test the actual ElegantRL evaluation function
try:
    import elegantrl.train.evaluator as evaluator_module
    
    print(f"\n📊 Testing ElegantRL get_rewards_and_steps with aggressive noise...")
    
    results = []
    for i in range(8):  # Test more episodes like ElegantRL does
        result, steps = evaluator_module.get_rewards_and_steps(env, actor)
        results.append(result)
        print(f"   Episode {i+1}: {result:.8f}")
    
    mean_result = np.mean(results)
    std_result = np.std(results)
    
    print(f"\n🎯 AGGRESSIVE NOISE RESULTS:")
    print(f"   Mean (avgR): {mean_result:.8f}")
    print(f"   Std (stdR):  {std_result:.8f}")
    print(f"   Min:         {np.min(results):.8f}")
    print(f"   Max:         {np.max(results):.8f}")
    print(f"   Range:       {np.max(results) - np.min(results):.8f}")
    
    # Check if this would be visible in ElegantRL's 2-decimal display
    if std_result > 0.005:  # 0.01 with 2 decimal places
        print(f"   ✅ EXCELLENT: Very strong variation! This should show in ElegantRL as avgR != 0.00")
    elif std_result > 0.0005:  # 0.001 might round to 0.00 in 2-decimal display
        print(f"   ✅ GOOD: Strong variation! Should show as non-zero avgR")
    elif std_result > 0.00005:
        print(f"   ⚠️  MODERATE: Some variation, but might still round to 0.00")
    else:
        print(f"   ❌ INSUFFICIENT: Still too small for ElegantRL display")
    
    # Test what ElegantRL's actual evaluator would see
    print(f"\n🔍 SIMULATING ELEGANTRL'S EVALUATION:")
    rewards_steps_list = [evaluator_module.get_rewards_and_steps(env, actor) for _ in range(4)]  # Default eval_times
    rewards_steps_ten = th.tensor(rewards_steps_list, dtype=th.float32)
    
    returns = rewards_steps_ten[:, 0]  # episodic cumulative returns
    steps = rewards_steps_ten[:, 1]    # episodic step number
    avg_r = returns.mean().item()
    std_r = returns.std().item() if len(returns) > 1 else 0.0
    avg_s = steps.mean().item()
    std_s = steps.std().item() if len(steps) > 1 else 0.0
    
    print(f"   ElegantRL avgR: {avg_r:.2f}")  # How it would display
    print(f"   ElegantRL stdR: {std_r:.1f}")  # How it would display
    print(f"   ElegantRL avgS: {avg_s:.0f}")
    print(f"   ElegantRL stdS: {std_s:.0f}")
    
    if abs(avg_r) >= 0.005 or std_r >= 0.05:
        print(f"   ✅ SUCCESS: ElegantRL would show non-zero avgR/stdR!")
    else:
        print(f"   ❌ STILL ROUNDING TO ZERO: Need even more aggressive noise")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n🚀 If this shows strong variation, run: python main.py tune --trials 2")