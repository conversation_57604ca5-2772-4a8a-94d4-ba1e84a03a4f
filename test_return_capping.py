#!/usr/bin/env python3
"""
Test the return capping fix.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

if __name__ == '__main__':
    print("🎯 TESTING RETURN CAPPING FIX")
    print("="*50)

    # Test extreme return scenarios
    scenarios = [
        ("Normal", 100, 110, "10% return"),
        ("High", 100, 500, "400% return → capped at 1000%"),
        ("NVDA-like", 1, 128, "12,700% return → capped at 1000%"),
        ("Loss", 100, 30, "-70% loss"),
        ("Extreme Loss", 100, 5, "-95% loss → capped at -80%")
    ]

    for name, start_price, end_price, description in scenarios:
        print(f"\n🧪 Test: {name} ({description})")
        
        # Create test data
        test_data = []
        for day in range(5):
            # Interpolate price between start and end
            price = start_price + (end_price - start_price) * (day / 4)
            test_data.append({
                'tic': 'TEST', 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
                'close': price, 'open': price, 'high': price*1.01, 'low': price*0.99,
                'volume': 1000000, 'day': day, 'sma_5': price, 'turbulence': 0.1, 'returns_1d': 0.1
            })

        df = pd.DataFrame(test_data).set_index('day')

        # Create environment
        env = AsymmetricTradingEnv(
            df=df, stock_dim=1, hmax=100, initial_amount=100000,
            num_stock_shares=[0], buy_cost_pct=[0.0], sell_cost_pct=[0.0],
            reward_scaling=1e-4, asymmetric_config=AsymmetricConfig(symbols=['TEST']),
            log_level='ERROR', tech_indicator_list=['sma_5', 'turbulence', 'returns_1d'],
            evaluation_noise_scale=0.0  # Disable noise for clean test
        )

        # Run episode
        state, _ = env.reset()
        action = np.array([1.0])  # Buy 100%

        for step in range(3):
            state, reward, done, truncated, info = env.step(action)
            action = np.array([0.0])  # Hold
            if done or truncated:
                break

        # Check results
        final_portfolio = info['total_asset']
        actual_return = (final_portfolio - env.initial_amount) / env.initial_amount
        cumulative_return = getattr(env, 'cumulative_returns', 0)
        unscaled_return = cumulative_return / env.reward_scaling

        theoretical_return = (end_price - start_price) / start_price

        print(f"   Price: ${start_price:.2f} → ${end_price:.2f}")
        print(f"   Theoretical return: {theoretical_return:.1%}")
        print(f"   Actual portfolio return: {actual_return:.1%}")
        print(f"   Capped return: {unscaled_return:.1%}")
        print(f"   cumulative_returns: {cumulative_return:.6f}")

        # Check if capping worked
        if theoretical_return > 10.0:  # >1000%
            expected_cap = 10.0
            print(f"   ✅ Expected capping at {expected_cap:.0%}: {abs(unscaled_return - expected_cap) < 0.1}")
        elif theoretical_return < -0.8:  # <-80%
            expected_cap = -0.8
            print(f"   ✅ Expected capping at {expected_cap:.0%}: {abs(unscaled_return - expected_cap) < 0.1}")
        else:
            print(f"   ✅ No capping needed: {abs(unscaled_return - actual_return) < 0.1}")

    print(f"\n📊 EXPECTED TRAINING RESULTS:")
    print(f"   Before fix: avgR = 82,882.87 (from 12,751% NVDA returns)")
    print(f"   After fix:  avgR ≤ {10.0 * 1e-4:.4f} (capped at 1000% return)")
    print(f"   Noise still works: stdR = ~5% of avgR")
    
    print(f"\n🎯 SUMMARY:")
    print(f"   • Root cause: NVDA $0.79→$101.49 = 12,751% return")
    print(f"   • Agent exploits extreme historical returns")
    print(f"   • Fix: Cap returns at reasonable ±80% to +1000%")
    print(f"   • Maintains variation but prevents unrealistic avgR")