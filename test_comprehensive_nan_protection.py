#!/usr/bin/env python3
"""
Comprehensive test of NaN protection in trading environment.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔬 COMPREHENSIVE NaN PROTECTION TEST")
print("="*50)

# Create test data with intentional problematic values
test_data = []
symbols = ['AAPL', 'MSFT', 'GOOGL']

for day in range(20):
    for symbol in symbols:
        # Add some problematic values to test robustness
        base_price = {'AAPL': 150, 'MSFT': 300, 'GOOGL': 100}[symbol]
        
        if day == 5:  # Day 5: Add some NaN values
            price = np.nan
            volume = np.nan
        elif day == 10:  # Day 10: Add some Inf values
            price = np.inf
            volume = np.inf
        elif day == 15:  # Day 15: Add extreme values
            price = base_price * 1e6  # Very large price
            volume = 1e12  # Very large volume
        else:
            price = base_price * (1 + 0.01 * day)  # Normal growth
            volume = 1000000
        
        test_data.append({
            'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
            'close': price, 'open': price, 'high': price * 1.01, 'low': price * 0.99,
            'volume': volume, 'day': day,
            # Technical indicators with potential issues
            'sma_5': price if not np.isnan(price) else 100.0,
            'sma_10': price * 0.98 if not np.isnan(price) else 100.0,
            'sma_20': price * 1.02 if not np.isnan(price) else 100.0,
            'sma_50': price * 0.95 if not np.isnan(price) else 100.0,
            'ema_12': price * 1.01 if not np.isnan(price) else 100.0,
            'ema_26': price * 0.99 if not np.isnan(price) else 100.0,
            'rsi_14': 50.0,
            'cci_20': 0.0,
            'macd_12_26_9': np.inf if day == 10 else 0.5,  # Intentional Inf
            'macds_12_26_9': -np.inf if day == 10 else -0.5,  # Intentional -Inf  
            'macdh_12_26_9': np.nan if day == 5 else 0.1,  # Intentional NaN
            'adx_14': 25.0,
            'dmp_14': 15.0,
            'dmn_14': 10.0,
            'bbl_20_2.0': price * 0.95 if not np.isnan(price) else 95.0,
            'bbm_20_2.0': price if not np.isnan(price) else 100.0,
            'bbu_20_2.0': price * 1.05 if not np.isnan(price) else 105.0,
            'bbb_20_2.0': 0.1,
            'bbp_20_2.0': 0.5,
            'obv': volume if not np.isnan(volume) else 1000000,
            'turbulence': 0.1,
            'price_range': 0.02,
            'price_position': 0.5,
            'returns_1d': 0.01,
            'returns_5d': 0.05,
            'returns_20d': 0.2,
            'volume_ma_20': volume if not np.isnan(volume) else 1000000,
            'volume_ratio': 1.0,
            'volatility_20d': 0.02,
            'vix_ma_5': 20.0,
            'vix_ma_20': 20.0,
            'vix_percentile_252': 0.5,
            'vix_change': 0.0,
            'vix_change_5d': 0.0,
            'vix_regime_numeric': 1.0
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['day', 'tic']).reset_index(drop=True)
df = df.set_index('day')

print(f"Created test dataset with intentional problematic values:")
print(f"  • Day 5: NaN prices and volumes")
print(f"  • Day 10: Inf prices and volumes, Inf in MACD indicators")
print(f"  • Day 15: Extreme large values (1e6x normal)")
print(f"  • Dataset: {len(df)} rows, {len(df.index.unique())} days")

# Test environment with problematic data
tech_indicators = [
    'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26',
    'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
    'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0',
    'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position',
    'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio',
    'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252',
    'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
]

try:
    print(f"\n🧪 Testing environment creation with problematic data...")
    
    env = AsymmetricTradingEnv(
        df=df, stock_dim=3, hmax=100, initial_amount=100000,
        num_stock_shares=[0, 0, 0], buy_cost_pct=[0.001, 0.001, 0.001],
        sell_cost_pct=[0.001, 0.001, 0.001], reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=symbols),
        log_level='WARNING',  # Reduce noise, show warnings
        tech_indicator_list=tech_indicators,
        evaluation_noise_scale=0.0
    )
    
    print(f"   ✅ Environment created successfully")
    print(f"   State space: {env.observation_space.shape[0]}")
    
    # Test reset
    print(f"\n🔄 Testing environment reset...")
    state, _ = env.reset()
    
    print(f"   Initial state shape: {state.shape}")
    print(f"   Initial state range: [{state.min():.6f}, {state.max():.6f}]")
    print(f"   Has NaN: {np.isnan(state).any()} ❌" if np.isnan(state).any() else "   Has NaN: False ✅")
    print(f"   Has Inf: {np.isinf(state).any()} ❌" if np.isinf(state).any() else "   Has Inf: False ✅")
    print(f"   All finite: {np.isfinite(state).all()} ✅" if np.isfinite(state).all() else "   All finite: False ❌")
    
    # Test multiple steps through problematic days
    print(f"\n🚶 Testing steps through problematic days...")
    
    problem_free_steps = 0
    total_steps = 0
    
    for step in range(15):  # Test through the problematic days
        action = np.random.uniform(-0.5, 0.5, 3)  # Random actions
        
        try:
            state, reward, done, truncated, info = env.step(action)
            total_steps += 1
            
            # Check for problems
            state_ok = np.isfinite(state).all()
            reward_ok = np.isfinite(reward)
            
            if state_ok and reward_ok:
                problem_free_steps += 1
                status = "✅"
            else:
                status = "❌"
                print(f"   Step {step}: Problems detected!")
                if not state_ok:
                    print(f"     State issues: NaN={np.isnan(state).any()}, Inf={np.isinf(state).any()}")
                if not reward_ok:
                    print(f"     Reward issues: {reward}")
            
            if step % 5 == 0:  # Log every 5th step
                current_day = getattr(env, 'day', 'unknown')
                portfolio = info.get('total_asset', 0)
                print(f"   Step {step} (Day {current_day}): Reward={reward:.8f}, Portfolio=${portfolio:.2f} {status}")
            
            if done or truncated:
                print(f"   Episode ended at step {step}")
                break
                
        except Exception as e:
            print(f"   Step {step}: Exception - {e}")
            break
    
    success_rate = (problem_free_steps / total_steps * 100) if total_steps > 0 else 0
    print(f"\n📊 Test Results:")
    print(f"   Total steps: {total_steps}")
    print(f"   Problem-free steps: {problem_free_steps}")
    print(f"   Success rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print(f"   🎉 ALL TESTS PASSED - NaN protection is working perfectly!")
    elif success_rate >= 90:
        print(f"   ✅ MOSTLY SUCCESSFUL - Minor issues detected")
    else:
        print(f"   ⚠️ ISSUES DETECTED - NaN protection needs improvement")
        
except Exception as e:
    print(f"❌ Critical error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n🛡️ PROTECTION LAYERS ACTIVE:")
print(f"   1. ✅ Base state cleaning in _get_state()")
print(f"   2. ✅ State component validation in step()")
print(f"   3. ✅ Technical indicator validation")
print(f"   4. ✅ Final state cleaning before assignment")
print(f"   5. ✅ Enhanced state NaN/Inf protection")
print(f"   6. ✅ State value clipping to ±1e6")
print(f"   7. ✅ Gradient norm clipping in SAC")
print(f"   8. ✅ Conservative hyperparameter ranges")