#!/usr/bin/env python3
"""
Test aggressive NaN protection with cleaner error filtering.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import logging
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

# Filter out the NaN error messages you don't want to see
class NoNaNFilter(logging.Filter):
    def filter(self, record):
        # Filter out specific NaN/Inf error messages
        unwanted_messages = [
            "NaN/inf values detected in enhanced_state",
            "NaN/Inf detected in base_state",
            "State contains NaN/Inf values"
        ]
        return not any(msg in record.getMessage() for msg in unwanted_messages)

# Apply filter to suppress NaN detection messages
logging.getLogger('AsymmetricTradingEnv').addFilter(NoNaNFilter())

print("🛡️ TESTING AGGRESSIVE NaN PROTECTION")
print("="*50)
print("Note: NaN detection messages are filtered for cleaner output")

# Create test data with extreme values
test_data = []
symbols = ['AAPL', 'MSFT', 'GOOGL']

for day in range(10):
    for symbol in symbols:
        # Create progressively more extreme values
        if day < 3:
            price = 100 + day * 10  # Normal
        elif day < 6:
            price = np.inf  # Infinite prices
        else:
            price = np.nan  # NaN prices
        
        test_data.append({
            'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
            'close': price, 'open': price, 'high': price, 'low': price,
            'volume': 1e15 if day > 5 else 1000000, 'day': day,
            'sma_5': np.inf if day == 4 else (price if np.isfinite(price) else 100),
            'sma_10': -np.inf if day == 5 else (price if np.isfinite(price) else 100),
            'sma_20': price if np.isfinite(price) else 100,
            'sma_50': price if np.isfinite(price) else 100,
            'ema_12': price if np.isfinite(price) else 100,
            'ema_26': price if np.isfinite(price) else 100,
            'rsi_14': 50.0, 'cci_20': 0.0,
            'macd_12_26_9': np.nan if day == 6 else 0.5,
            'macds_12_26_9': np.inf if day == 7 else -0.5,
            'macdh_12_26_9': -np.inf if day == 8 else 0.1,
            'adx_14': 25.0, 'dmp_14': 15.0, 'dmn_14': 10.0,
            'bbl_20_2.0': 95.0, 'bbm_20_2.0': 100.0, 'bbu_20_2.0': 105.0,
            'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5,
            'obv': 1e20 if day > 6 else 1000000,  # Extreme volume
            'turbulence': 0.1, 'price_range': 0.02, 'price_position': 0.5,
            'returns_1d': 0.01, 'returns_5d': 0.05, 'returns_20d': 0.2,
            'volume_ma_20': 1000000, 'volume_ratio': 1.0, 'volatility_20d': 0.02,
            'vix_ma_5': 20.0, 'vix_ma_20': 20.0, 'vix_percentile_252': 0.5,
            'vix_change': 0.0, 'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['day', 'tic']).reset_index(drop=True)
df = df.set_index('day')

print(f"Created extreme test dataset:")
print(f"  • Days 0-2: Normal values")
print(f"  • Days 3-5: Infinite prices and indicators")
print(f"  • Days 6-9: NaN prices and extreme volumes")

# Test environment
tech_indicators = ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26',
                  'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
                  'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0',
                  'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position',
                  'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio',
                  'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252',
                  'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']

try:
    print(f"\n🧪 Testing environment with extreme data...")
    
    env = AsymmetricTradingEnv(
        df=df, stock_dim=3, hmax=100, initial_amount=100000,
        num_stock_shares=[0, 0, 0], buy_cost_pct=[0.001, 0.001, 0.001],
        sell_cost_pct=[0.001, 0.001, 0.001], reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=symbols),
        log_level='ERROR',  # Only show errors
        tech_indicator_list=tech_indicators,
        evaluation_noise_scale=0.0
    )
    
    print(f"   ✅ Environment created successfully")
    
    # Test reset
    state, _ = env.reset()
    
    print(f"   Reset state: shape={state.shape}, range=[{state.min():.2f}, {state.max():.2f}]")
    print(f"   Clean state: NaN={np.isnan(state).any()}, Inf={np.isinf(state).any()}")
    
    # Test steps through extreme data
    print(f"\n🚶 Testing steps through extreme days...")
    
    clean_steps = 0
    for step in range(8):
        action = np.random.uniform(-0.1, 0.1, 3)
        
        state, reward, done, truncated, info = env.step(action)
        
        # Check if state and reward are clean
        state_clean = np.isfinite(state).all()
        reward_clean = np.isfinite(reward)
        
        if state_clean and reward_clean:
            clean_steps += 1
            status = "✅"
        else:
            status = "❌"
            
        day = getattr(env, 'day', 'unknown')
        portfolio = info.get('total_asset', 0)
        
        print(f"   Step {step} (Day {day}): Reward={reward:.6f}, Portfolio=${portfolio:.2f}, Range=[{state.min():.1f}, {state.max():.1f}] {status}")
        
        if done or truncated:
            break
    
    success_rate = (clean_steps / (step + 1)) * 100
    print(f"\n📊 Results:")
    print(f"   Clean steps: {clean_steps}/{step + 1}")
    print(f"   Success rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print(f"   🎉 PERFECT! All states and rewards are finite.")
    else:
        print(f"   ⚠️ Some non-finite values detected.")
        
except Exception as e:
    print(f"❌ Critical error: {e}")

print(f"\n💡 TO FILTER NaN MESSAGES IN YOUR TRAINING:")
print(f"Add this code at the start of your script:")
print(f"""
import logging

class NoNaNFilter(logging.Filter):
    def filter(self, record):
        unwanted = ["NaN/inf values detected", "State contains NaN/Inf"]
        return not any(msg in record.getMessage() for msg in unwanted)

logging.getLogger('AsymmetricTradingEnv').addFilter(NoNaNFilter())
""")

print(f"\n🛡️ PROTECTION SUMMARY:")
print(f"   • Values clamped to [-100, +100] range")
print(f"   • All NaN/Inf converted to finite values")
print(f"   • Multi-layer state cleaning")
print(f"   • Aggressive reward protection")
print(f"   • Error message filtering available")