#!/usr/bin/env python3
"""
Add this code to the start of your training script to filter NaN error messages.
"""

import logging

class NoNaNFilter(logging.Filter):
    """Filter to suppress NaN/Inf detection messages that are now safely handled."""
    
    def filter(self, record):
        # List of message patterns to filter out
        unwanted_messages = [
            "NaN/inf values detected in enhanced_state",
            "NaN/Inf detected in base_state", 
            "State contains NaN/Inf values",
            "NaN/Inf detected in basic state components",
            "Non-finite portfolio_value",
            "Non-finite reward",
            "NaN/inf values detected in final features array",
            "Residual NaN/inf in asymmetric features",
            "Replaced", "NaN/inf values with 0.0",
            "Cleaned base_state of NaN/Inf values",
            "Non-finite", "using 0.0",
            "Invalid previous_portfolio_value"
        ]
        
        # Return False to filter out (not show) messages containing these patterns
        return not any(msg in record.getMessage() for msg in unwanted_messages)

# Apply the filter to the AsymmetricTradingEnv logger
logging.getLogger('AsymmetricTradingEnv').addFilter(NoNaNFilter())

print("✅ Comprehensive NaN error message filtering enabled")
print("    • All NaN/Inf detection messages suppressed")
print("    • Values are still being cleaned automatically")
print("    • Training will proceed with clean output")

# Optional: Also filter warnings if you want even cleaner output
class NoWarningFilter(logging.Filter):
    """Optional filter to suppress all WARNING level messages from environment."""
    
    def filter(self, record):
        return record.levelno != logging.WARNING

# Uncomment the next line if you want to suppress ALL warnings from the environment
# logging.getLogger('AsymmetricTradingEnv').addFilter(NoWarningFilter())