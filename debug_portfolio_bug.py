#!/usr/bin/env python3
"""
Debug script to reproduce the portfolio value bug.
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def debug_portfolio_bug():
    """Debug the portfolio value calculation bug."""
    
    print("Debugging portfolio value calculation bug...")
    
    try:
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        
        # Create minimal test data - exactly like the backtest data
        tickers = ['AAPL', 'ADBE', 'AMZN', 'ASML', 'AVGO', 'GOOGL', 'META', 'MSFT', 'NVDA', 'TSLA']
        stock_dim = 10
        
        # Create data with real prices similar to backtest
        data_rows = []
        prices = [123.47, 336.92, 85.82, 538.42, 53.24, 88.70, 124.15, 234.81, 14.30, 108.10]
        
        for day in range(5):  # Just 5 days
            for i, ticker in enumerate(tickers):
                data_rows.append({
                    'date': pd.Timestamp(f'2024-01-0{day+1}'),
                    'tic': ticker,
                    'close': prices[i] * (1 + np.random.randn() * 0.01),  # Small random variation
                    'open': prices[i] * (1 + np.random.randn() * 0.01),
                    'high': prices[i] * 1.02,
                    'low': prices[i] * 0.98,
                    'volume': 1000000,
                    # Add minimal technical indicators
                    'sma_5': prices[i],
                    'sma_10': prices[i], 
                    'rsi_14': 50.0,
                    'macd_12_26_9': 0.0,
                    'turbulence': 0.1
                })
        
        df = pd.DataFrame(data_rows)
        df['day'] = df['date'].factorize()[0]
        df = df.set_index('day')
        
        print(f"Created test data with shape: {df.shape}")
        print(f"Day 0 close prices: {df.loc[0]['close'].tolist()}")
        
        # Create environment with same config as backtest
        asymmetric_config = AsymmetricConfig(symbols=tickers)
        
        env = AsymmetricTradingEnv(
            df=df,
            stock_dim=stock_dim,
            hmax=100,
            initial_amount=100000,  # Same as backtest
            num_stock_shares=[0] * stock_dim,
            buy_cost_pct=[0.001] * stock_dim,
            sell_cost_pct=[0.001] * stock_dim,
            asymmetric_config=asymmetric_config,
            tech_indicator_list=['sma_5', 'sma_10', 'rsi_14', 'macd_12_26_9', 'turbulence'],
            log_level="INFO"
        )
        
        print("\n=== INITIAL STATE ===")
        obs = env.reset()
        if isinstance(obs, tuple):
            obs = obs[0]
        
        # Extract state components
        cash = env.state[0]
        holdings = env.state[1:stock_dim+1] 
        prices = env.state[stock_dim+1:2*stock_dim+1]
        portfolio_value = cash + np.sum(holdings * prices)
        
        print(f"Initial Cash: ${cash:.2f}")
        print(f"Initial Holdings: {holdings}")
        print(f"State Prices: {prices}")
        print(f"Portfolio Value: ${portfolio_value:.2f}")
        
        print("\n=== STEP 1: BUY ACTIONS ===")
        # Try the same actions as in the backtest log: [ 1. -1. -1.  1. -1. -1.  1. -1. -1. -1.]
        actions = np.array([1.0, -1.0, -1.0, 1.0, -1.0, -1.0, 1.0, -1.0, -1.0, -1.0])
        print(f"Actions: {actions}")
        
        obs, reward, done, truncated, info = env.step(actions)
        
        # Extract new state
        new_cash = env.state[0]
        new_holdings = env.state[1:stock_dim+1]
        new_prices = env.state[stock_dim+1:2*stock_dim+1]
        new_portfolio_value = new_cash + np.sum(new_holdings * new_prices)
        
        print(f"New Cash: ${new_cash:.2f}")
        print(f"New Holdings: {new_holdings}")
        print(f"New State Prices: {new_prices}")
        print(f"Holdings * Prices: ${np.sum(new_holdings * new_prices):.2f}")
        print(f"New Portfolio Value: ${new_portfolio_value:.2f}")
        print(f"Portfolio Change: ${new_portfolio_value - portfolio_value:.2f}")
        print(f"Reward: {reward:.6f}")
        
        # Check if cash was deducted properly
        cash_spent = cash - new_cash
        holdings_value = np.sum(new_holdings * new_prices)
        print(f"\nCash spent: ${cash_spent:.2f}")
        print(f"Holdings value: ${holdings_value:.2f}")
        print(f"Ratio: {holdings_value / cash_spent:.2f}")
        
        # The bug: holdings look like stock prices, not share quantities!
        print(f"\n=== BUG ANALYSIS ===")
        print(f"Holdings values: {new_holdings}")
        print(f"Real stock prices from data: {df.loc[1]['close'].tolist()}")
        print("PROBLEM: Holdings contain dollar amounts, not share quantities!")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_portfolio_bug()