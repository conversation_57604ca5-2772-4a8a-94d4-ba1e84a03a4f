# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Status: PRODUCTION-READY ✅
This is a sophisticated FinRL-based trading system implementing an **Asymmetric Return Profile strategy** that has undergone extensive development and debugging. The system is **99% complete** and production-ready after resolving critical training and backtesting issues in breakthrough sessions.

## Development Commands

### Environment Setup
```bash
# Create conda environment
conda env create -f environment.yml
conda activate finrl-trading

# Install dependencies
pip install -r requirements.txt
```

### Core Development Workflow
```bash
# Main CLI interface - shows all available commands
python main.py --help

# Data pipeline
python main.py get-data          # Fetch market data
python main.py process-data      # Add technical indicators

# Model development
python main.py tune              # Hyperparameter optimization with Optuna
python main.py train             # Train SAC agent
python main.py backtest          # Run backtesting with pyfolio

# Alternative training entry points
python train_asymmetric.py       # Standalone training script
python asymmetric_trading_bot.py000  # Original implementation reference
```

### Testing & Debugging
```bash
# Run individual test components
python test_asymmetric_env_fix.py
python test_backtest_fix.py
python test_numpy_fix.py
python test_reward_fix.py
python test_vix_fix.py
```

## Critical System Knowledge

### ⚠️ Important Training Insight
The "constant avgR" issue previously observed was **NOT a core training problem** but an ElegantRL evaluator display issue. The core training system is **fully operational** - individual environments show varying rewards and portfolio growth from ~100K to ~500K is confirmed.

### Memory Bank Context
The `memory-bank/` directory contains critical project history including breakthrough sessions, technical decisions, and resolved issues. Key files:
- `breakthrough_session_dec2024.md`: Major fixes for training and backtesting
- `session_summary_core_training_validation.md`: Training system validation
- `technicalIssues.md`: Resolved system issues and solutions

## Architecture Overview

This is a **reinforcement learning-based trading system** implementing an **Asymmetric Return Profile strategy** targeting top tech stocks. The system uses SAC (Soft Actor-Critic) algorithm to achieve flat returns during market downturns while capturing positive returns during upturns.

### Core Components

**Data Pipeline** (`src/data/`): 
- Multi-source data fetching (yfinance primary, Alpaca fallback)
- Smart CSV caching with timestamp validation 
- Feature engineering with 35+ technical indicators via pandas_ta
- VIX integration for market regime detection

**RL Models** (`src/models/`):
- SAC agent implementation using ElegantRL
- Optuna-based hyperparameter optimization
- Model checkpointing and persistence

**Trading Strategy** (`src/strategies/`):
- Asymmetric strategy with 2:1 upside/downside capture ratio
- VIX-based volatility regime adaptation
- Dynamic position sizing and risk management

**Trading Environment** (`src/trading/`):
- Custom FinRL environment with 401-dimensional state space
- Asymmetric reward functions favoring upside capture
- Enhanced state representation with technical and asymmetric features

**Configuration** (`config/`):
- Pydantic-based type-safe configuration management
- Hierarchical config structure with environment variable overrides
- Centralized settings for all components

### Key Data Flow

1. **Data**: Market data → Cache → Technical indicators → VIX integration → Feature engineering
2. **Training**: Processed data → AsymmetricTradingEnv → SAC Agent → Hyperparameter optimization → Model persistence  
3. **Evaluation**: Test data → Trained model → Backtesting engine → Performance metrics → Visualization

### Important Implementation Details

- **Asymmetric Rewards**: Implemented in `asymmetric_env.py` with separate bull/bear market behavior
- **State Space**: 401 dimensions including price data, technical indicators, and asymmetric features
- **Caching**: Smart caching system in `src/data/cache.py` with MD5-based validation
- **Configuration**: All parameters centralized in `config/settings.py` with Pydantic validation
- **Logging**: Structured logging via loguru with both console and file output

### Model Training Specifics

- Uses ElegantRL's SAC implementation for continuous action spaces
- Training checkpoints saved in `models/checkpoints/`
- Hyperparameter optimization results stored in `models/checkpoints/optimization/`
- Tensorboard logging for training visualization
- **CRITICAL**: Training system validates with portfolio growth from ~100K to ~500K
- Individual environment rewards vary correctly (0.068838, 0.090109, etc.)

### Backtesting Integration

- Built on pyfolio for professional-grade performance analysis
- Results saved in `results/backtests/` with JSON, CSV, and PNG outputs
- Comprehensive metrics including Sharpe ratio, drawdown, and benchmark comparison
- **RESOLVED**: Enhanced portfolio tracking with multiple extraction methods

### Key Architectural Decisions

1. **Target Stocks**: Top 10 tech stocks (AAPL, MSFT, GOOGL, AMZN, META, NVDA, TSLA, AVGO, ADBE, ASML)
2. **Asymmetric Reward Design**: 2:1 upside/downside capture ratio with fear index integration
3. **Feature Engineering**: 45+ technical indicators with VIX integration for market regime detection
4. **Environment Design**: 431-dimensional state space with 5-day warmup mechanism
5. **Model Management**: Separation of training (`models/checkpoints/`) and production (`models/saved/`) models

### Production Readiness Checklist ✅

- [x] Core training system validated and operational
- [x] Hyperparameter optimization working with Optuna
- [x] Backtesting system functional with pyfolio
- [x] Data pipeline robust with yfinance and VIX integration
- [x] Model management system with checkpoints and persistence
- [x] Comprehensive logging and error handling
- [x] CLI interface with all operational modes
- [x] Technical issues resolved from breakthrough sessions