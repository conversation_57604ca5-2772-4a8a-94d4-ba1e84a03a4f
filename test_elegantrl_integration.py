#!/usr/bin/env python3
"""
Test ElegantRL integration to debug avgS=0 issue
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import torch
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig
from elegantrl.train.evaluator import get_rewards_and_steps

print("🔍 Testing ElegantRL integration...")

# Create simple test data
test_data = []
prices = [100, 101, 102, 103, 104, 105]  

for i, price in enumerate(prices):
    for stock in ['AAPL', 'MSFT']:
        test_data.append({
            'tic': stock,
            'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': price, 'open': price, 'high': price+1, 'low': price-1, 'volume': 1000000,
            'sma_5': price, 'sma_10': price, 'sma_20': price, 'rsi_14': 50.0,
            'macd_12_26_9': 0.0, 'ema_12': price, 'ema_26': price, 'cci_20': 0.0,
            'adx_14': 30.0, 'bbl_20_2.0': price-3, 'bbm_20_2.0': price,
            'bbu_20_2.0': price+3, 'obv': 1000000, 'turbulence': 0.1
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📊 Test data: {len(df)} rows, {len(df.index.unique())} days")

# Test 1: Manual environment test
print(f"\n🧪 Test 1: Manual environment testing")
try:
    env = AsymmetricTradingEnv(
        df=df,
        stock_dim=2,
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0, 0],
        buy_cost_pct=[0.001, 0.001],
        sell_cost_pct=[0.001, 0.001],
        reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=['AAPL', 'MSFT']),
        log_level='ERROR',
        tech_indicator_list=['sma_5', 'sma_10', 'sma_20', 'rsi_14', 'macd_12_26_9', 'ema_12', 'ema_26', 'cci_20', 'adx_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'obv', 'turbulence']
    )
    
    print(f"✅ Environment created: max_step={env.max_step}")
    
    # Manual episode test
    state, info = env.reset()
    step_count = 0
    total_reward = 0
    
    for i in range(10):  # Run for 10 steps max
        action = np.array([0.1, 0.1])  # Small buy signal
        state, reward, terminated, truncated, info = env.step(action)
        step_count += 1
        total_reward += reward
        
        if terminated or truncated:
            print(f"Episode ended at step {step_count}: terminated={terminated}, truncated={truncated}")
            break
    
    print(f"Manual test: {step_count} steps, total_reward={total_reward:.8f}")
    
except Exception as e:
    print(f"❌ Manual test failed: {e}")
    import traceback
    traceback.print_exc()

# Test 2: ElegantRL evaluator test
print(f"\n🧪 Test 2: ElegantRL evaluator testing")
try:
    # Create a dummy actor that just returns zeros
    class DummyActor(torch.nn.Module):
        def __init__(self, state_dim, action_dim):
            super().__init__()
            self.fc = torch.nn.Linear(state_dim, action_dim)
            
        def forward(self, state):
            # Return small positive actions
            return torch.tanh(self.fc(state)) * 0.1
    
    env = AsymmetricTradingEnv(
        df=df,
        stock_dim=2,
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0, 0],
        buy_cost_pct=[0.001, 0.001],
        sell_cost_pct=[0.001, 0.001],
        reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=['AAPL', 'MSFT']),
        log_level='ERROR',
        tech_indicator_list=['sma_5', 'sma_10', 'sma_20', 'rsi_14', 'macd_12_26_9', 'ema_12', 'ema_26', 'cci_20', 'adx_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'obv', 'turbulence']
    )
    
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.shape[0]
    
    print(f"ElegantRL test: state_dim={state_dim}, action_dim={action_dim}")
    
    actor = DummyActor(state_dim, action_dim)
    
    # Use ElegantRL's evaluator
    cumulative_reward, episode_steps = get_rewards_and_steps(env, actor)
    
    print(f"✅ ElegantRL test completed:")
    print(f"  Episode steps: {episode_steps}")
    print(f"  Cumulative reward: {cumulative_reward:.8f}")
    print(f"  Expected steps: {env.max_step}")
    
    if episode_steps == 0:
        print("❌ CRITICAL: ElegantRL evaluator shows 0 steps!")
        print("This explains why avgS=0 in training")
    elif episode_steps < env.max_step / 2:
        print(f"⚠️  WARNING: Episode ended early ({episode_steps} vs {env.max_step} expected)")
    else:
        print("✅ Episode ran reasonable length")
        
except Exception as e:
    print(f"❌ ElegantRL test failed: {e}")
    import traceback
    traceback.print_exc()

print(f"\n📊 Summary:")
print(f"If manual test works but ElegantRL test fails, the issue is in ElegantRL integration")
print(f"If both fail, the issue is in the environment itself")