# Core Python packages
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# Machine Learning and Deep Learning
torch>=2.0.0
torchvision>=0.15.0
scikit-learn>=1.3.0

# Financial Data and Trading
alpaca-trade-api>=3.0.0
yfinance>=0.2.0
pandas-ta>=0.3.14b
pandas_market_calendars

# Reinforcement Learning
gym>=0.26.0
stable-baselines3>=2.0.0

# FinRL and ElegantRL (from GitHub)
# git+https://github.com/AI4Finance-Foundation/FinRL.git
# git+https://github.com/AI4Finance-Foundation/ElegantRL.git

# Configuration and Environment Management
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# CLI and User Interface
click>=8.0.0
tqdm>=4.65.0

# Logging
loguru>=0.7.0

# Hyperparameter Optimization
optuna>=3.0.0
optuna-dashboard>=0.13.0

# Data Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0

# Web and API
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Time Series Analysis
statsmodels>=0.14.0
arch>=6.2.0

# Performance and Monitoring
psutil>=5.9.0
memory-profiler>=0.60.0

# Data Validation
cerberus>=1.3.4
jsonschema>=4.17.0

# Parallel Processing
joblib>=1.3.0
multiprocess>=0.70.0

# Development and Testing
pytest>=7.0.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-asyncio>=0.21.0
factory-boy>=3.3.0

# Code Quality
black>=23.0.0
flake8>=6.0.0
mypy>=1.4.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0

# Jupyter
jupyter>=1.0.0
ipykernel>=6.24.0

