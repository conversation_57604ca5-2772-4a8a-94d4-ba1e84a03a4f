#!/usr/bin/env python3
"""
Test script to verify AsymmetricTradingEnv initialization fixes.
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_asymmetric_env_initialization():
    """Test AsymmetricTradingEnv initialization with various configurations."""
    
    print("Testing AsymmetricTradingEnv initialization fixes...")
    
    try:
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        tickers = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'TSLA', 'AVGO', 'ADBE', 'ASML']
        
        data_rows = []
        for date in dates:
            for ticker in tickers:
                data_rows.append({
                    'date': date,
                    'tic': ticker,
                    'close': 100.0 + np.random.randn() * 5,
                    'open': 100.0 + np.random.randn() * 5,
                    'high': 105.0 + np.random.randn() * 5,
                    'low': 95.0 + np.random.randn() * 5,
                    'volume': 1000000 + np.random.randint(-100000, 100000),
                    # Add technical indicators
                    'sma_5': 100.0 + np.random.randn() * 2,
                    'sma_10': 100.0 + np.random.randn() * 2,
                    'sma_20': 100.0 + np.random.randn() * 2,
                    'sma_50': 100.0 + np.random.randn() * 2,
                    'ema_12': 100.0 + np.random.randn() * 2,
                    'ema_26': 100.0 + np.random.randn() * 2,
                    'rsi_14': 50.0 + np.random.randn() * 10,
                    'cci_20': np.random.randn() * 50,
                    'macd_12_26_9': np.random.randn() * 2,
                    'macdh_12_26_9': np.random.randn() * 1,
                    'macds_12_26_9': np.random.randn() * 1,
                    'adx_14': 25.0 + np.random.randn() * 10,
                    'dmp_14': 25.0 + np.random.randn() * 10,
                    'dmn_14': 25.0 + np.random.randn() * 10,
                    'bbl_20_2.0': 95.0 + np.random.randn() * 2,
                    'bbm_20_2.0': 100.0 + np.random.randn() * 2,
                    'bbu_20_2.0': 105.0 + np.random.randn() * 2,
                    'bbb_20_2.0': 10.0 + np.random.randn() * 2,
                    'bbp_20_2.0': 0.5 + np.random.randn() * 0.2,
                    'obv': 1000000 + np.random.randint(-100000, 100000),
                    'price_range': 0.05 + np.random.randn() * 0.01,
                    'price_position': 0.5 + np.random.randn() * 0.2,
                    'returns_1d': np.random.randn() * 0.02,
                    'returns_5d': np.random.randn() * 0.05,
                    'returns_20d': np.random.randn() * 0.1,
                    'volume_ma_20': 1000000 + np.random.randint(-100000, 100000),
                    'volume_ratio': 1.0 + np.random.randn() * 0.2,
                    'volatility_20d': 0.2 + np.random.randn() * 0.05,
                    'vix_ma_5': 20.0 + np.random.randn() * 5,
                    'vix_ma_20': 20.0 + np.random.randn() * 5,
                    'vix_percentile_252': 0.5 + np.random.randn() * 0.2,
                    'vix_change': np.random.randn() * 2,
                    'vix_change_5d': np.random.randn() * 5,
                    'vix_regime_numeric': np.random.choice([0, 1, 2]),
                    'turbulence': np.random.randn() * 0.1
                })
        
        df = pd.DataFrame(data_rows)
        print(f"Created test dataframe with {len(df)} rows and {len(df.columns)} columns")
        print(f"Unique tickers: {df.tic.unique()}")
        print(f"Date range: {df.date.min()} to {df.date.max()}")
        
        # Test configuration
        stock_dim = 10
        hmax = 100
        initial_amount = 1000000
        num_stock_shares = [0] * stock_dim
        buy_cost_pct = [0.001] * stock_dim
        sell_cost_pct = [0.001] * stock_dim
        
        asymmetric_config = AsymmetricConfig(symbols=tickers)
        
        print("\nTesting AsymmetricTradingEnv initialization...")
        print(f"stock_dim: {stock_dim}")
        print(f"hmax: {hmax}")
        print(f"initial_amount: {initial_amount}")
        print(f"num_stock_shares: {num_stock_shares}")
        print(f"buy_cost_pct: {buy_cost_pct}")
        print(f"sell_cost_pct: {sell_cost_pct}")
        
        # Initialize environment
        env = AsymmetricTradingEnv(
            df=df,
            stock_dim=stock_dim,
            hmax=hmax,
            initial_amount=initial_amount,
            num_stock_shares=num_stock_shares,
            buy_cost_pct=buy_cost_pct,
            sell_cost_pct=sell_cost_pct,
            asymmetric_config=asymmetric_config,
            log_level="DEBUG"
        )
        
        print("✅ AsymmetricTradingEnv initialized successfully!")
        print(f"Environment state_space: {env.state_space}")
        print(f"Environment action_space: {env.action_space}")
        print(f"Environment observation_space: {env.observation_space}")
        
        # Test reset
        print("\nTesting environment reset...")
        obs = env.reset()
        if isinstance(obs, tuple):
            obs = obs[0]
        print(f"✅ Environment reset successful! Observation shape: {obs.shape}")
        
        # Test step
        print("\nTesting environment step...")
        actions = np.zeros(stock_dim)  # No-op actions
        result = env.step(actions)
        print(f"✅ Environment step successful! Result length: {len(result)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_asymmetric_env_initialization()
    if success:
        print("\n🎉 All tests passed! AsymmetricTradingEnv initialization fixes are working.")
        sys.exit(0)
    else:
        print("\n💥 Tests failed! There are still issues with AsymmetricTradingEnv initialization.")
        sys.exit(1)
