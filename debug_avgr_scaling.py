#!/usr/bin/env python3
"""
Debug the extreme avgR values and constant behavior.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔍 DEBUGGING EXTREME AVGR VALUES")
print("="*50)

# Create test data
test_data = []
for i in range(10):
    price = 100 + i * 0.1  # Small price increase
    test_data.append({
        'tic': 'TEST', 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
        'close': price, 'open': price, 'high': price*1.01, 'low': price*0.99,
        'volume': 1000000, 'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
        'ema_12': price, 'ema_26': price, 'rsi_14': 50.0, 'macd_12_26_9': 0.0,
        'macds_12_26_9': 0.0, 'macdh_12_26_9': 0.0, 'cci_20': 0.0, 'adx_14': 30.0,
        'dmp_14': 25.0, 'dmn_14': 25.0, 'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price,
        'bbu_20_2.0': price*1.05, 'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000,
        'turbulence': 0.1, 'price_range': 0.02, 'price_position': 0.5, 'returns_1d': 0.001,
        'returns_5d': 0.005, 'returns_20d': 0.02, 'volume_ma_20': 1000000, 'volume_ratio': 1.0,
        'volatility_20d': 0.02, 'vix_ma_5': 20.0, 'vix_ma_20': 20.0, 'vix_percentile_252': 0.5,
        'vix_change': 0.0, 'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
    })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

# Test different reward_scaling values
for reward_scaling in [1.0, 0.1, 0.01, 1e-4]:
    print(f"\n🧪 Testing reward_scaling = {reward_scaling}")
    
    env = AsymmetricTradingEnv(
        df=df, stock_dim=1, hmax=100, initial_amount=100000,
        num_stock_shares=[0], buy_cost_pct=[0.0], sell_cost_pct=[0.0],
        reward_scaling=reward_scaling, asymmetric_config=AsymmetricConfig(symbols=['TEST']),
        log_level='ERROR',
        tech_indicator_list=[
            'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 
            'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
            'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 
            'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 
            'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 
            'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 
            'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
        ]
    )
    
    # Run episode
    state, _ = env.reset()
    action = np.array([1.0])  # Buy all
    
    total_step_rewards = 0
    for step in range(8):
        state, reward, done, truncated, info = env.step(action)
        total_step_rewards += reward
        
        if step == 0:
            print(f"    Step {step}: reward={reward:.8f}, portfolio=${info['total_asset']:.2f}")
        action = np.array([0.0])  # Hold after first step
        
        if done or truncated:
            break
    
    # Check final values
    cumulative_returns = getattr(env, 'cumulative_returns', None)
    final_portfolio = info['total_asset']
    manual_cumulative = (final_portfolio - env.initial_amount) / env.initial_amount
    
    print(f"    Sum of step rewards: {total_step_rewards:.8f}")
    print(f"    Env cumulative_returns: {cumulative_returns:.8f}")
    print(f"    Manual cumulative: {manual_cumulative:.8f}")
    print(f"    Portfolio: ${env.initial_amount} → ${final_portfolio:.2f}")
    print(f"    Reasonable avgR: {abs(cumulative_returns) < 1000}")

print(f"\n🎯 ISSUE ANALYSIS:")
print(f"The avgR = 175,877,248 suggests:")
print(f"  1. Portfolio grew by huge amount OR")
print(f"  2. reward_scaling amplified small return to huge number")
print(f"  3. cumulative_returns calculation error")

print(f"\n🔧 SOLUTION:")
print(f"  - Use reward_scaling = 1e-4 for reasonable avgR magnitude")
print(f"  - Ensure cumulative_returns is percentage, not absolute dollars")
print(f"  - Add more evaluation noise to break stdR = 0.0 determinism")