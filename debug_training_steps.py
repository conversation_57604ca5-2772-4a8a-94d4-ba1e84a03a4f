#!/usr/bin/env python3
"""
Debug training step counting issue
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig
from models.sac_agent import SACAgent

print("🔍 Debugging training step counting...")

# Create test data
test_data = []
for i in range(20):  # 20 days
    for stock in ['AAPL', 'MSFT']:
        price = 100 + i  # Linear price increase
        test_data.append({
            'tic': stock,
            'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': price, 'open': price, 'high': price+1, 'low': price-1, 'volume': 1000000,
            'sma_5': price, 'sma_10': price, 'sma_20': price, 'rsi_14': 50.0,
            'macd_12_26_9': 0.0, 'ema_12': price, 'ema_26': price, 'cci_20': 0.0,
            'adx_14': 30.0, 'bbl_20_2.0': price-3, 'bbm_20_2.0': price,
            'bbu_20_2.0': price+3, 'obv': 1000000, 'turbulence': 0.1
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📊 Test data: {len(df)} rows, {len(df.index.unique())} days")

# Create environment
try:
    env = AsymmetricTradingEnv(
        df=df,
        stock_dim=2,
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0, 0],
        buy_cost_pct=[0.001, 0.001],
        sell_cost_pct=[0.001, 0.001],
        reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=['AAPL', 'MSFT']),
        log_level='INFO',  # Enable detailed logging
        tech_indicator_list=['sma_5', 'sma_10', 'sma_20', 'rsi_14', 'macd_12_26_9', 'ema_12', 'ema_26', 'cci_20', 'adx_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'obv', 'turbulence']
    )
    
    print(f"✅ Environment: max_step={env.max_step}, state_dim={env.observation_space.shape[0]}, action_dim={env.action_space.shape[0]}")
    
    # Create a simple SAC agent
    agent_config = {
        'learning_rate': 0.001,
        'gamma': 0.99,
        'tau': 0.005,
        'alpha': 0.2,
        'batch_size': 64,
        'buffer_size': 10000,
        'net_dims': [64, 64],
        'repeat_times': 1.0,
        'reward_scale': 1.0,
        'if_per': False,
        'if_off_policy': True,
        'checkpoint_dir': '/tmp/debug_sac'
    }
    
    sac_agent = SACAgent(
        state_dim=env.observation_space.shape[0],
        action_dim=env.action_space.shape[0],
        config=agent_config,
        asymmetric_config=AsymmetricConfig(symbols=['AAPL', 'MSFT'])
    )
    
    print(f"✅ SAC agent created")
    
    # Run a short training session
    print(f"\n🏃 Running short training session...")
    
    results = sac_agent.train(
        env=env,
        total_timesteps=1000,  # Very short training
        eval_env=None,  # Skip evaluation for this debug
        eval_freq=10000,  # No evaluation
        save_freq=10000,  # No saving
        model_dir='/tmp/debug_sac'
    )
    
    print(f"✅ Training completed: {results}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    
print(f"\n📊 If this works without avgS=0, the issue might be in:")
print(f"  1. The data preparation for main training")  
print(f"  2. The specific ElegantRL configuration being used")
print(f"  3. The environment setup in main.py vs this debug script")