#!/usr/bin/env python3
"""
Debug the extreme portfolio values causing avgR = 82,882.87
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from pathlib import Path

print("🔍 DEBUGGING EXTREME PORTFOLIO VALUES")
print("="*50)

print("1. Analyzing the extreme avgR = 82,882.87...")
print(f"   With reward_scaling = 1e-4:")
print(f"   Unscaled return = 82,882.87 / 1e-4 = {82882.87 / 1e-4:,.0f}%")
print(f"   Portfolio: $100,000 → ${100000 * (1 + 82882.87/1e-4):,.0f}")
print(f"   This is clearly impossible!")

print(f"\n2. Checking actual training data...")

# Load the real training data to check for extreme values
data_dir = Path("data/processed")
try:
    processed_file = data_dir / "processed_data.csv"
    if processed_file.exists():
        df = pd.read_csv(processed_file)
        
        print(f"   Loaded data: {len(df)} rows")
        print(f"   Date range: {df['date'].min()} to {df['date'].max()}")
        print(f"   Symbols: {list(df['tic'].unique())}")
        
        # Check for extreme price values
        print(f"\n   Price analysis:")
        print(f"   Close price range: ${df['close'].min():.2f} to ${df['close'].max():.2f}")
        print(f"   Close price mean: ${df['close'].mean():.2f}")
        print(f"   Close price std: ${df['close'].std():.2f}")
        
        # Check for extreme price changes
        df_sorted = df.sort_values(['tic', 'date'])
        df_sorted['price_change'] = df_sorted.groupby('tic')['close'].pct_change()
        
        extreme_changes = df_sorted[abs(df_sorted['price_change']) > 0.5]  # >50% daily change
        if len(extreme_changes) > 0:
            print(f"\n   ⚠️  Found {len(extreme_changes)} extreme price changes (>50% daily):")
            for _, row in extreme_changes.head(5).iterrows():
                print(f"     {row['tic']} on {row['date']}: {row['price_change']*100:.1f}% change")
                
        # Check returns columns
        return_cols = [col for col in df.columns if 'return' in col.lower()]
        if return_cols:
            print(f"\n   Return columns: {return_cols}")
            for col in return_cols:
                extreme_returns = df[abs(df[col]) > 1.0]  # >100% returns
                if len(extreme_returns) > 0:
                    print(f"     {col}: {len(extreme_returns)} extreme values (>100%)")
                    print(f"       Range: {df[col].min():.3f} to {df[col].max():.3f}")
        
    else:
        print(f"   ❌ No processed data found at {processed_file}")
        
except Exception as e:
    print(f"   ❌ Error loading data: {e}")

print(f"\n3. Testing portfolio calculation with controlled data...")

try:
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    
    # Create controlled test data with reasonable price movements
    test_data = []
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    
    for day in range(10):  # Short test
        for symbol in symbols:
            # Start at $100, grow 0.1% daily (reasonable)
            price = 100 * (1.001 ** day)
            test_data.append({
                'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
                'close': price, 'open': price, 'high': price*1.01, 'low': price*0.99,
                'volume': 1000000, 'day': day,
                'sma_5': price, 'turbulence': 0.1, 'returns_1d': 0.001
            })
    
    df = pd.DataFrame(test_data).set_index('day')
    
    # Create environment with debug
    env = AsymmetricTradingEnv(
        df=df, stock_dim=3, hmax=100, initial_amount=100000,
        num_stock_shares=[0, 0, 0], buy_cost_pct=[0.001, 0.001, 0.001],
        sell_cost_pct=[0.001, 0.001, 0.001], reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=symbols),
        log_level='ERROR', tech_indicator_list=['sma_5', 'turbulence', 'returns_1d'],
        evaluation_noise_scale=0.0  # Disable noise for clean debug
    )
    
    print(f"   Environment created: initial_amount=${env.initial_amount}")
    print(f"   Reward scaling: {env.reward_scaling}")
    
    # Run controlled episode
    state, _ = env.reset()
    print(f"\n   Initial portfolio: ${env.initial_amount}")
    
    # Buy some stocks
    action = np.array([0.5, 0.3, 0.2])  # Allocate across 3 stocks
    
    portfolio_values = [env.initial_amount]
    
    for step in range(8):
        state, reward, done, truncated, info = env.step(action)
        portfolio_value = info.get('total_asset', 0)
        portfolio_values.append(portfolio_value)
        
        cumulative_return = getattr(env, 'cumulative_returns', 0)
        unscaled_return = cumulative_return / env.reward_scaling if env.reward_scaling != 0 else 0
        
        print(f"   Step {step+1}: Portfolio=${portfolio_value:.2f}, "
              f"cumulative_returns={cumulative_return:.8f}, "
              f"unscaled={unscaled_return:.6f}")
        
        action = np.array([0.0, 0.0, 0.0])  # Hold after first buy
        
        if done or truncated:
            break
    
    final_portfolio = portfolio_values[-1]
    actual_return = (final_portfolio - env.initial_amount) / env.initial_amount
    final_cumulative_returns = getattr(env, 'cumulative_returns', 0)
    final_unscaled = final_cumulative_returns / env.reward_scaling
    
    print(f"\n   📊 RESULTS:")
    print(f"   Final portfolio: ${final_portfolio:.2f}")
    print(f"   Actual return: {actual_return:.6f} ({actual_return*100:.3f}%)")
    print(f"   Env cumulative_returns: {final_cumulative_returns:.8f}")
    print(f"   Env unscaled return: {final_unscaled:.6f}")
    print(f"   Match: {abs(actual_return - final_unscaled) < 1e-6}")
    
    if abs(final_cumulative_returns) > 1.0:
        print(f"   ❌ PROBLEM: cumulative_returns = {final_cumulative_returns:.2f} is too large!")
        print(f"       This would translate to avgR = {final_cumulative_returns} in training")
    else:
        print(f"   ✅ cumulative_returns magnitude looks reasonable")

except Exception as e:
    print(f"   ❌ Error in portfolio test: {e}")
    import traceback
    traceback.print_exc()

print(f"\n🎯 LIKELY CAUSES OF EXTREME avgR:")
print(f"1. Data corruption: Extreme price values in training data")
print(f"2. Calculation error: Portfolio calculation bug")
print(f"3. Price data issue: Missing price normalization")
print(f"4. Environment bug: Incorrect portfolio tracking")

print(f"\n💡 INVESTIGATION STEPS:")
print(f"1. Check training data for extreme price values")
print(f"2. Verify portfolio calculation logic")
print(f"3. Check if price data needs preprocessing")
print(f"4. Compare with working environment implementation")