#!/usr/bin/env python3
"""
Debug script to find where data gets truncated from 1762 days to 124 days.
"""

import sys
import pandas as pd
import numpy as np
sys.path.insert(0, 'src')

print("🔍 DEBUGGING DATA TRUNCATION ISSUE")
print("="*50)

try:
    # Import required modules
    from data.processor import DataProcessor
    from data.fetcher import DataFetcher
    from data.cache import DataCache
    from config.settings import Settings
    
    # Initialize components
    settings = Settings()
    cache = DataCache(settings.data.cache_dir)
    fetcher = DataFetcher(cache)
    
    print("✅ Initialized components")
    
    # Create processor
    processor = DataProcessor(
        fetcher=fetcher,
        settings_obj=settings,
        tech_indicator_list=settings.data.tech_indicator_list,
        vix_features=settings.data.vix_features,
        include_vix=settings.data.include_vix
    )
    
    print("✅ Created DataProcessor")
    
    # Process stock data
    print("\n📊 Processing stock data...")
    processed_data = processor.process_stock_data()
    
    print(f"✅ Processed data shape: {processed_data.shape}")
    print(f"✅ Date range: {processed_data['date'].min()} to {processed_data['date'].max()}")
    print(f"✅ Unique dates: {processed_data['date'].nunique()}")
    print(f"✅ Unique symbols: {processed_data['tic'].nunique()}")
    
    # Apply date filtering like in main.py
    print("\n🔽 Applying date filtering...")
    train_start = '2016-01-01'
    train_end = '2022-12-31'
    
    processed_data['date'] = pd.to_datetime(processed_data['date'])
    train_df = processed_data[
        (processed_data['date'] >= train_start) & 
        (processed_data['date'] <= train_end)
    ].reset_index(drop=True)
    
    print(f"✅ Filtered training data shape: {train_df.shape}")
    print(f"✅ Unique dates after filtering: {train_df['date'].nunique()}")
    print(f"✅ Date range after filtering: {train_df['date'].min()} to {train_df['date'].max()}")
    print(f"✅ Records per symbol: {train_df.shape[0] / train_df['tic'].nunique():.0f}")
    
    # Apply the factorize transformation
    print("\n🔄 Applying factorize transformation...")
    print(f"Before factorize: {train_df['date'].nunique()} unique dates")
    
    train_df['day'] = train_df['date'].factorize()[0]
    print(f"After factorize: {train_df['day'].nunique()} unique day values")
    print(f"Day range: {train_df['day'].min()} to {train_df['day'].max()}")
    
    # Set index
    train_df_indexed = train_df.set_index('day')
    print(f"After set_index: {len(train_df_indexed.index.unique())} unique index values")
    print(f"Index range: {train_df_indexed.index.min()} to {train_df_indexed.index.max()}")
    
    # Check specific days
    print("\n🔍 Checking specific index values...")
    max_day = train_df_indexed.index.max()
    print(f"Max day index: {max_day}")
    
    # Try accessing the data like the environment does
    try:
        day_data = train_df_indexed.loc[0, :]
        print(f"✅ Can access day 0: {type(day_data)}")
    except Exception as e:
        print(f"❌ Cannot access day 0: {e}")
    
    try:
        day_data = train_df_indexed.loc[max_day, :]
        print(f"✅ Can access max day ({max_day}): {type(day_data)}")
    except Exception as e:
        print(f"❌ Cannot access max day ({max_day}): {e}")
    
    try:
        day_data = train_df_indexed.loc[123, :]
        print(f"✅ Can access day 123: {type(day_data)}")
    except Exception as e:
        print(f"❌ Cannot access day 123: {e}")
    
    try:
        day_data = train_df_indexed.loc[124, :]
        print(f"✅ Can access day 124: {type(day_data)}")
    except Exception as e:
        print(f"❌ Cannot access day 124: {e}")
    
    # Summary
    print("\n📋 SUMMARY:")
    print(f"Expected days: ~1762 (2016-2022)")
    print(f"Actual days: {train_df['day'].nunique()}")
    print(f"Issue: {'✅ NO ISSUE' if train_df['day'].nunique() > 1500 else '❌ DATA TRUNCATED'}")
    
    if train_df['day'].nunique() <= 200:
        print("\n🚨 CRITICAL: Data is severely truncated!")
        print("📝 Possible causes:")
        print("  1. Date filtering is too restrictive")
        print("  2. Data files are incomplete")
        print("  3. Processing pipeline has bugs")
        print("  4. Cache corruption")
        
        # Check a few sample records
        print(f"\n📅 Sample dates in data:")
        sample_dates = train_df['date'].drop_duplicates().sort_values().head(10)
        for i, date in enumerate(sample_dates):
            print(f"  Day {i}: {date}")
    
except Exception as e:
    print(f"❌ Error during debugging: {e}")
    import traceback
    traceback.print_exc()