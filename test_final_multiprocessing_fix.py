#!/usr/bin/env python3
"""
Test the final multiprocessing evaluation noise fix.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

if __name__ == '__main__':
    print("🎯 TESTING FINAL MULTIPROCESSING FIX")
    print("="*50)
    print("Testing evaluation_noise_scale parameter in environment constructor")

    # Create test data
    test_data = []
    for i in range(20):
        test_data.append({
            'tic': 'TEST', 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': 100 + i * 0.5, 'open': 100 + i * 0.5, 'high': 101 + i * 0.5, 'low': 99 + i * 0.5,
            'volume': 1000000, 'day': i, 'sma_5': 100 + i * 0.5, 'turbulence': 0.1, 'returns_1d': 0.005
        })

    df = pd.DataFrame(test_data).set_index('day')

    print(f"1. Testing environment without noise (default)...")
    
    env1 = AsymmetricTradingEnv(
        df=df, stock_dim=1, hmax=100, initial_amount=100000,
        num_stock_shares=[0], buy_cost_pct=[0.001], sell_cost_pct=[0.001],
        reward_scaling=1e-4, asymmetric_config=AsymmetricConfig(symbols=['TEST']),
        log_level='ERROR', tech_indicator_list=['sma_5', 'turbulence', 'returns_1d'],
        # evaluation_noise_scale default is 0.0
    )
    
    print(f"   Environment 1 noise scale: {env1._evaluation_noise_scale}")
    
    # Run episodes
    results1 = []
    for episode in range(3):
        state, _ = env1.reset()
        action = np.array([0.8])
        for step in range(18):
            state, reward, done, truncated, info = env1.step(action)
            action = np.array([0.0])
            if done or truncated:
                break
        results1.append(getattr(env1, 'cumulative_returns', 0))
    
    print(f"   Results: {[f'{r:.8f}' for r in results1]}")
    print(f"   Std: {np.std(results1):.8f}")

    print(f"\n2. Testing environment with noise parameter...")
    
    env2 = AsymmetricTradingEnv(
        df=df, stock_dim=1, hmax=100, initial_amount=100000,
        num_stock_shares=[0], buy_cost_pct=[0.001], sell_cost_pct=[0.001],
        reward_scaling=1e-4, asymmetric_config=AsymmetricConfig(symbols=['TEST']),
        log_level='ERROR', tech_indicator_list=['sma_5', 'turbulence', 'returns_1d'],
        evaluation_noise_scale=0.05  # Enable noise via constructor parameter
    )
    
    print(f"   Environment 2 noise scale: {env2._evaluation_noise_scale}")
    
    # Run episodes
    results2 = []
    for episode in range(3):
        state, _ = env2.reset()
        action = np.array([0.8])
        for step in range(18):
            state, reward, done, truncated, info = env2.step(action)
            action = np.array([0.0])
            if done or truncated:
                break
        results2.append(getattr(env2, 'cumulative_returns', 0))
    
    print(f"   Results: {[f'{r:.8f}' for r in results2]}")
    print(f"   Std: {np.std(results2):.8f}")

    print(f"\n3. Testing environment args simulation (what ElegantRL workers do)...")
    
    # Simulate what ElegantRL does - create environment from args
    env_args = {
        'df': df, 'stock_dim': 1, 'hmax': 100, 'initial_amount': 100000,
        'num_stock_shares': [0], 'buy_cost_pct': [0.001], 'sell_cost_pct': [0.001],
        'reward_scaling': 1e-4, 'asymmetric_config': AsymmetricConfig(symbols=['TEST']),
        'log_level': 'ERROR', 'tech_indicator_list': ['sma_5', 'turbulence', 'returns_1d'],
        'evaluation_noise_scale': 0.05  # This is what SAC agent now includes
    }
    
    env3 = AsymmetricTradingEnv(**env_args)
    print(f"   Environment 3 (from args) noise scale: {env3._evaluation_noise_scale}")
    
    # Run episodes
    results3 = []
    for episode in range(3):
        state, _ = env3.reset()
        action = np.array([0.8])
        for step in range(18):
            state, reward, done, truncated, info = env3.step(action)
            action = np.array([0.0])
            if done or truncated:
                break
        results3.append(getattr(env3, 'cumulative_returns', 0))
    
    print(f"   Results: {[f'{r:.8f}' for r in results3]}")
    print(f"   Std: {np.std(results3):.8f}")

    # Analysis
    std1 = np.std(results1)
    std2 = np.std(results2)
    std3 = np.std(results3)

    print(f"\n📊 COMPARISON:")
    print(f"   Default (no noise): std = {std1:.8f}")
    print(f"   Constructor param:  std = {std2:.8f}")
    print(f"   From env_args:      std = {std3:.8f}")

    success = (std2 > std1 * 5) and (std3 > std1 * 5)
    
    print(f"\n✅ SUCCESS CRITERIA:")
    print(f"   Noise adds variation: {success} ({'✅' if success else '❌'})")
    print(f"   Constructor works: {std2 > std1 * 5} ({'✅' if std2 > std1 * 5 else '❌'})")
    print(f"   env_args works: {std3 > std1 * 5} ({'✅' if std3 > std1 * 5 else '❌'})")

    if success:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"   • evaluation_noise_scale parameter works in constructor")
        print(f"   • env_args properly includes noise settings")
        print(f"   • Worker processes will inherit noise settings")
        print(f"   • Training should now show varying avgR and stdR > 0.0")
    else:
        print(f"\n⚠️  Issue still exists - need further debugging")

    print(f"\n📋 EXPECTED TRAINING RESULT:")
    print(f"   python main.py train should now show:")
    print(f"   ID Step Time | avgR stdR avgS stdS |")
    print(f"   -1 2.05e+03 | 0.0042 0.0003 1761 0 |  (varying values)")
    print(f"   -1 2.25e+04 | 0.0039 0.0004 1761 0 |  (different values)")
    print(f"   Instead of constant avgR and stdR = 0.0")