#!/usr/bin/env python3
"""
Debug extreme price movements in training data.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from pathlib import Path

print("🔍 DEBUGGING EXTREME PRICE MOVEMENTS")
print("="*50)

# Load the real training data
data_dir = Path("data/processed")
processed_file = data_dir / "processed_data.csv"

if processed_file.exists():
    df = pd.read_csv(processed_file)
    df['date'] = pd.to_datetime(df['date'])
    
    print(f"Loaded data: {len(df)} rows, {len(df['tic'].unique())} stocks")
    
    print(f"\n1. Analyzing price extremes by stock...")
    
    # Analyze each stock
    for symbol in sorted(df['tic'].unique()):
        stock_data = df[df['tic'] == symbol].sort_values('date')
        
        min_price = stock_data['close'].min()
        max_price = stock_data['close'].max()
        first_price = stock_data['close'].iloc[0]
        last_price = stock_data['close'].iloc[-1]
        
        total_return = (last_price - first_price) / first_price
        price_ratio = max_price / min_price
        
        print(f"   {symbol}:")
        print(f"     Price range: ${min_price:.2f} - ${max_price:.2f} (ratio: {price_ratio:.1f}x)")
        print(f"     Period return: {total_return:.1%} (${first_price:.2f} → ${last_price:.2f})")
        
        # Check for extreme daily changes
        stock_data = stock_data.copy()
        stock_data['daily_return'] = stock_data['close'].pct_change()
        extreme_days = stock_data[abs(stock_data['daily_return']) > 0.3]  # >30% daily change
        
        if len(extreme_days) > 0:
            print(f"     ⚠️  {len(extreme_days)} extreme daily changes (>30%):")
            for _, day in extreme_days.head(3).iterrows():
                print(f"       {day['date'].strftime('%Y-%m-%d')}: {day['daily_return']:.1%}")
        
        # Check if this could cause portfolio explosion
        if price_ratio > 100 or total_return > 10:  # 100x price increase or 1000% return
            print(f"     🚨 EXTREME: This stock could cause portfolio explosion!")
    
    print(f"\n2. Simulating portfolio with extreme stock...")
    
    # Find the most extreme stock
    stock_returns = []
    for symbol in df['tic'].unique():
        stock_data = df[df['tic'] == symbol].sort_values('date')
        first_price = stock_data['close'].iloc[0]
        last_price = stock_data['close'].iloc[-1]
        total_return = (last_price - first_price) / first_price
        stock_returns.append((symbol, total_return, first_price, last_price))
    
    # Sort by return
    stock_returns.sort(key=lambda x: x[1], reverse=True)
    
    print(f"   Top performers:")
    for i, (symbol, ret, first, last) in enumerate(stock_returns[:3]):
        print(f"     {i+1}. {symbol}: {ret:.1%} (${first:.2f} → ${last:.2f})")
    
    # Check what happens if agent buys the extreme stock
    extreme_symbol, extreme_return, first_price, last_price = stock_returns[0]
    
    print(f"\n3. Portfolio simulation with {extreme_symbol}...")
    print(f"   If agent bought ${100000} of {extreme_symbol} at ${first_price:.2f}:")
    print(f"   Shares bought: {100000/first_price:.0f}")
    print(f"   Final value: ${100000 * (1 + extreme_return):,.0f}")
    print(f"   Portfolio return: {extreme_return:.1%}")
    print(f"   With 1e-4 scaling: {extreme_return * 1e-4:.6f}")
    
    if extreme_return * 1e-4 > 1000:
        print(f"   🚨 This explains avgR = {extreme_return * 1e-4:.0f}!")
    
    print(f"\n4. Checking if real training uses this data...")
    
    # Check data processing and filtering
    print(f"   Original data date range: {df['date'].min()} to {df['date'].max()}")
    print(f"   Total trading days: {df['date'].nunique()}")
    
    # Check if training splits this data  
    total_days = df['date'].nunique()
    train_days = int(total_days * 0.7)  # Assuming 70% train split
    val_days = total_days - train_days
    
    print(f"   Likely split: {train_days} train days, {val_days} val days")
    print(f"   Training avgS shows 1761 days")
    
    if train_days != 1761:
        print(f"   ⚠️  Mismatch: calculated {train_days} vs observed 1761")

else:
    print("❌ No processed data found")

print(f"\n🎯 LIKELY ROOT CAUSE:")
print(f"   Training data includes stocks with extreme returns (1000%+)")
print(f"   Agent learns to buy these stocks → massive portfolio gains")
print(f"   Even with 1e-4 scaling, these become huge avgR values")

print(f"\n💡 SOLUTIONS:")
print(f"1. Cap maximum single-day returns (e.g., ±50%)")
print(f"2. Use log returns instead of percentage returns") 
print(f"3. Add portfolio value constraints")
print(f"4. Filter out penny stocks or extreme outliers")
print(f"5. Use relative performance vs market benchmark")