#!/usr/bin/env python3
"""
Test extreme noise scale and larger reward scaling to get visible avgR variation.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import torch as th
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔧 TESTING EXTREME NOISE + LARGER REWARDS FOR AVGR VARIATION")
print("=" * 65)

# Create test data with larger price movements
test_data = []
for day in range(15):
    for symbol in ['AAPL']:
        # Create larger price movements to generate bigger rewards
        base_price = 150
        price_change = 0.05 * day  # 5% change per day instead of 1%
        price = base_price * (1 + price_change)
        
        test_data.append({
            'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
            'close': price, 'open': price * 0.99, 'high': price * 1.02, 'low': price * 0.98,
            'volume': 1000000, 'day': day,
            'sma_5': price, 'rsi_14': 50, 'turbulence': 0.1
        })

df = pd.DataFrame(test_data).set_index('day')

# Create environment with MUCH larger reward scaling and extreme noise
env = AsymmetricTradingEnv(
    df=df, stock_dim=1, hmax=100, initial_amount=100000,
    num_stock_shares=[0], buy_cost_pct=[0.001], sell_cost_pct=[0.001],
    asymmetric_config=AsymmetricConfig(symbols=['AAPL']),
    log_level='ERROR', tech_indicator_list=['sma_5', 'rsi_14', 'turbulence'],
    reward_scaling=1.0,  # MUCH larger reward scaling (was 1e-4)
    evaluation_noise_scale=1.0  # EXTREME noise scale
)

# Also enable extreme noise in the centralized manager
from utils.evaluation_noise import EvaluationNoiseManager
EvaluationNoiseManager.enable(noise_scale=1.0)  # Extreme noise
print(f"Enabled EXTREME evaluation noise: scale=1.0")

class TestActor(th.nn.Module):
    def __init__(self):
        super().__init__()
        self.dummy = th.nn.Parameter(th.tensor([0.0]))
    def forward(self, state):
        return th.tensor([[0.1]], dtype=th.float32)

actor = TestActor()

print(f"Created environment with reward_scaling=1.0 and evaluation_noise_scale=1.0")

# Test the actual ElegantRL evaluation function
try:
    import elegantrl.train.evaluator as evaluator_module
    
    print(f"\n📊 Testing ElegantRL with EXTREME noise and larger rewards...")
    
    results = []
    for i in range(6):
        result, steps = evaluator_module.get_rewards_and_steps(env, actor)
        results.append(result)
        print(f"   Episode {i+1}: {result:.6f}")
    
    mean_result = np.mean(results)
    std_result = np.std(results)
    
    print(f"\n🎯 EXTREME NOISE + LARGE REWARDS RESULTS:")
    print(f"   Mean (avgR): {mean_result:.6f}")
    print(f"   Std (stdR):  {std_result:.6f}")
    print(f"   Min:         {np.min(results):.6f}")
    print(f"   Max:         {np.max(results):.6f}")
    print(f"   Range:       {np.max(results) - np.min(results):.6f}")
    
    # Test what ElegantRL's evaluator would see with these larger values
    print(f"\n🔍 SIMULATING ELEGANTRL'S EVALUATION WITH LARGE REWARDS:")
    rewards_steps_list = [evaluator_module.get_rewards_and_steps(env, actor) for _ in range(4)]
    rewards_steps_ten = th.tensor(rewards_steps_list, dtype=th.float32)
    
    returns = rewards_steps_ten[:, 0]
    steps = rewards_steps_ten[:, 1]
    avg_r = returns.mean().item()
    std_r = returns.std().item() if len(returns) > 1 else 0.0
    avg_s = steps.mean().item()
    std_s = steps.std().item() if len(steps) > 1 else 0.0
    
    print(f"   Raw avgR: {avg_r:.6f}")
    print(f"   Raw stdR: {std_r:.6f}")
    print(f"   ElegantRL display avgR: {avg_r:.2f}")  # How it would display
    print(f"   ElegantRL display stdR: {std_r:.1f}")  # How it would display
    
    if abs(avg_r) >= 0.005 or std_r >= 0.05:
        print(f"   ✅ BREAKTHROUGH: ElegantRL would show non-zero avgR/stdR!")
        print(f"   🎉 This should solve the 0.00 problem!")
    else:
        print(f"   ⚠️  Still small but better: {avg_r:.2f} / {std_r:.1f}")
        
    # Check noise manager state
    manager = EvaluationNoiseManager.get_instance()
    print(f"\n🔧 Noise Manager Settings:")
    print(f"   Base noise scale: {manager.noise_scale}")
    print(f"   Action noise scale: {manager.action_noise_scale}")
    print(f"   Return noise scale: {manager.return_noise_scale}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n🚀 If this shows visible avgR variation, the fix is working!")
print(f"Run: python main.py tune --trials 2")