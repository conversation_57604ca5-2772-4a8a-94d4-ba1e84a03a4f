#!/usr/bin/env python3
"""
Focused debug script for ElegantRL evaluator constant avgR issue.

This script tests the exact get_rewards_and_steps function that ElegantRL uses
and identifies why it returns constant rewards.
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any
import traceback
import torch as th

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def create_test_data(num_days: int = 30) -> pd.DataFrame:
    """Create test data with clear price variations."""
    print(f"Creating test data: {num_days} days")
    
    dates = pd.date_range('2023-01-01', periods=num_days, freq='D')
    
    data = []
    for i, date in enumerate(dates):
        # Create clearly varying prices to ensure reward differences
        base_price = 100 + i * 2  # Linear price increase
        price_variation = 5 * np.sin(i * 0.3)  # Sine wave variation
        price = base_price + price_variation
        
        data.append({
            'date': date,
            'tic': 'TEST',
            'open': price * 0.99,
            'high': price * 1.02,
            'low': price * 0.97,
            'close': price,
            'volume': 1000000,
            # Technical indicators
            'sma_5': price * (1 + np.random.normal(0, 0.01)),
            'ema_12': price * (1 + np.random.normal(0, 0.01)),
            'rsi_14': 30 + np.random.uniform(0, 40),
            'macd_12_26_9': np.random.normal(0, 0.5),
            'adx_14': 20 + np.random.uniform(0, 30),
            'turbulence': np.random.uniform(0, 2)
        })
    
    df = pd.DataFrame(data)
    df['date'] = pd.to_datetime(df['date'])
    df['day'] = df['date'].factorize()[0]
    df = df.set_index('day')
    
    print(f"✅ Created test data with price range: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
    return df

def test_core_environment_issue():
    """Test the core environment to identify the root issue."""
    print("\n" + "="*60)
    print("🔍 CORE ENVIRONMENT ISSUE ANALYSIS")
    print("="*60)
    
    try:
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        
        # Create test data with clear variations
        test_data = create_test_data(num_days=20)
        
        asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            volatility_lookback=5
        )
        
        # Create environment
        env = AsymmetricTradingEnv(
            df=test_data,
            stock_dim=1,
            hmax=100,
            initial_amount=100000,
            num_stock_shares=[0],
            buy_cost_pct=[0.001],
            sell_cost_pct=[0.001],
            reward_scaling=1e-4,
            tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
            asymmetric_config=asymmetric_config,
            log_level="ERROR"
        )
        
        print(f"Environment created successfully")
        print(f"Max step: {getattr(env, 'max_step', 'Not set')}")
        
        # Test basic step functionality with very different actions
        print(f"\n📊 Testing step functionality with varying actions:")
        
        state, info = env.reset()
        print(f"Initial state shape: {state.shape}")
        
        # Check what happens in the step method by tracking portfolio values
        actions = [
            np.array([0.5]),   # Strong buy
            np.array([0.0]),   # Hold
            np.array([-0.5])   # Strong sell
        ]
        
        for i, action in enumerate(actions):
            print(f"\n   Action {i+1}: {action[0]}")
            
            # Get portfolio before step
            if hasattr(env, 'state') and len(env.state) > env.stock_dim * 2:
                cash_before = env.state[0]
                holdings_before = env.state[1:env.stock_dim+1]
                prices_before = env.state[env.stock_dim+1:2*env.stock_dim+1]
                portfolio_before = cash_before + np.sum(holdings_before * prices_before)
                print(f"     Before: Portfolio=${portfolio_before:.2f}, Cash=${cash_before:.2f}, Holdings={holdings_before}")
            
            # Take step
            next_state, reward, terminated, truncated, step_info = env.step(action)
            print(f"     Step result: Reward={reward:.8f}, Terminated={terminated}, Truncated={truncated}")
            
            # Get portfolio after step
            if hasattr(env, 'state') and len(env.state) > env.stock_dim * 2:
                cash_after = env.state[0]
                holdings_after = env.state[1:env.stock_dim+1]
                prices_after = env.state[env.stock_dim+1:2*env.stock_dim+1]
                portfolio_after = cash_after + np.sum(holdings_after * prices_after)
                portfolio_change = portfolio_after - portfolio_before if 'portfolio_before' in locals() else 0
                print(f"     After:  Portfolio=${portfolio_after:.2f}, Cash=${cash_after:.2f}, Holdings={holdings_after}")
                print(f"     Portfolio change: ${portfolio_change:.2f}")
            
            # Check step_info
            print(f"     Step info: {step_info}")
            
            if terminated or truncated:
                print(f"     Environment terminated, resetting...")
                state, info = env.reset()
                
        # Test reward calculation by manually checking portfolio changes
        print(f"\n🔧 Manual reward calculation test:")
        
        env.reset()
        
        # Take a significant action and check detailed reward calculation
        big_buy_action = np.array([0.8])
        print(f"Testing big buy action: {big_buy_action}")
        
        # Check environment state before
        print(f"Environment reward_scaling: {env.reward_scaling}")
        print(f"Environment initial_amount: {env.initial_amount}")
        
        next_state, reward, terminated, truncated, step_info = env.step(big_buy_action)
        print(f"Reward from big buy: {reward:.10f}")
        
        # Check if reward scaling is the issue
        if abs(reward) < 1e-10:
            print("❌ CRITICAL ISSUE: Reward is essentially zero")
            print("   This suggests either:")
            print("   1. No portfolio value change is occurring")
            print("   2. Reward scaling is too aggressive")
            print("   3. Portfolio calculation is broken")
        else:
            print("✅ Reward is non-zero, variation should occur")
        
        return abs(reward) > 1e-10
        
    except Exception as e:
        print(f"❌ Error in core environment test: {e}")
        traceback.print_exc()
        return False

def test_elegantrl_get_rewards_and_steps():
    """Test the exact get_rewards_and_steps function that ElegantRL uses."""
    print("\n" + "="*60)
    print("🧪 ELEGANTRL get_rewards_and_steps FUNCTION TEST")
    print("="*60)
    
    try:
        # Import the exact function ElegantRL uses
        sys.path.append('/app/workspaces/ElegantRL')
        from elegantrl.train.evaluator import get_rewards_and_steps
        
        print("✅ Successfully imported ElegantRL get_rewards_and_steps")
        
        # Create environment
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        
        test_data = create_test_data(num_days=15)
        
        asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            volatility_lookback=5
        )
        
        env = AsymmetricTradingEnv(
            df=test_data,
            stock_dim=1,
            hmax=100,
            initial_amount=100000,
            num_stock_shares=[0],
            buy_cost_pct=[0.001],
            sell_cost_pct=[0.001],
            reward_scaling=1e-4,
            tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
            asymmetric_config=asymmetric_config,
            log_level="ERROR"
        )
        
        # Set max_step attribute that ElegantRL expects
        env.max_step = len(test_data) - 2
        print(f"Set env.max_step = {env.max_step}")
        
        # Create a simple actor that produces varying actions
        class TestActor(th.nn.Module):
            def __init__(self, action_dim):
                super().__init__()
                self.action_dim = action_dim
                self.step_count = 0
                
            def forward(self, state):
                self.step_count += 1
                # Create deterministic but varying actions based on step count
                action_value = 0.3 * np.sin(self.step_count * 0.5)
                return th.tensor([[action_value]], dtype=th.float32)
                
            def parameters(self):
                # Return a generator with at least one parameter for device detection
                yield th.tensor([0.0], requires_grad=True)
        
        actor = TestActor(env.action_space.shape[0])
        
        print(f"\n🎯 Testing get_rewards_and_steps function:")
        
        # Run multiple episodes to test reward variation
        episodes_data = []
        
        for episode in range(5):
            print(f"\n   Episode {episode + 1}:")
            
            try:
                # Reset actor step count for each episode
                actor.step_count = 0
                
                # Call the exact function ElegantRL uses
                cumulative_return, episode_steps = get_rewards_and_steps(env, actor, if_render=False)
                
                episodes_data.append((cumulative_return, episode_steps))
                print(f"     Cumulative return: {cumulative_return:.8f}")
                print(f"     Episode steps: {episode_steps}")
                
            except Exception as e:
                print(f"     ❌ Error in episode {episode + 1}: {e}")
                traceback.print_exc()
        
        # Analyze results
        if episodes_data:
            returns = [data[0] for data in episodes_data]
            steps = [data[1] for data in episodes_data]
            
            print(f"\n📈 Episode Analysis:")
            print(f"   Returns: {returns}")
            print(f"   Steps: {steps}")
            print(f"   Return mean: {np.mean(returns):.8f}")
            print(f"   Return std: {np.std(returns):.8f}")
            
            # This is how ElegantRL calculates avgR
            avg_r = np.mean(returns)
            print(f"   avgR (as ElegantRL calculates): {avg_r:.8f}")
            
            variation_exists = np.std(returns) > 1e-10
            
            if variation_exists:
                print(f"✅ SUCCESS: Returns vary between episodes")
                print(f"   The constant avgR issue may be elsewhere in the training loop")
            else:
                print(f"❌ ISSUE: Returns are constant between episodes")
                print(f"   This explains the constant avgR in ElegantRL output")
            
            return variation_exists
        else:
            print(f"❌ No episode data collected")
            return False
        
    except Exception as e:
        print(f"❌ Error in ElegantRL function test: {e}")
        traceback.print_exc()
        return False

def test_step_by_step_debugging():
    """Debug step-by-step what happens in get_rewards_and_steps."""
    print("\n" + "="*60)
    print("🔍 STEP-BY-STEP DEBUGGING")
    print("="*60)
    
    try:
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        
        test_data = create_test_data(num_days=10)
        
        asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            volatility_lookback=5
        )
        
        env = AsymmetricTradingEnv(
            df=test_data,
            stock_dim=1,
            hmax=100,
            initial_amount=100000,
            num_stock_shares=[0],
            buy_cost_pct=[0.001],
            sell_cost_pct=[0.001],
            reward_scaling=1e-4,
            tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
            asymmetric_config=asymmetric_config,
            log_level="ERROR"
        )
        
        env.max_step = len(test_data) - 2
        
        # Manual implementation of get_rewards_and_steps to debug each step
        print(f"\n🔧 Manual get_rewards_and_steps implementation:")
        
        class SimpleActor:
            def __init__(self):
                self.call_count = 0
                
            def parameters(self):
                yield th.tensor([0.0])
                
            def __call__(self, state_tensor):
                self.call_count += 1
                # Return a different action each time
                action_val = 0.1 * self.call_count / 10.0  # Gradually increasing action
                return th.tensor([[action_val]], dtype=th.float32)
        
        actor = SimpleActor()
        
        # Reset environment and track everything
        state, info_dict = env.reset()
        print(f"   Environment reset - initial state shape: {state.shape}")
        
        episode_steps = 0
        cumulative_returns = 0.0
        
        device = next(actor.parameters()).device
        print(f"   Actor device: {device}")
        
        max_step = env.max_step
        print(f"   Max steps: {max_step}")
        
        step_details = []
        
        for episode_steps in range(max_step):
            print(f"\n   Step {episode_steps + 1}:")
            
            # Get action from actor (replicating ElegantRL logic)
            tensor_state = th.as_tensor(state, dtype=th.float32, device=device).unsqueeze(0)
            print(f"     State tensor shape: {tensor_state.shape}")
            
            tensor_action = actor(tensor_state)
            action = tensor_action.detach().cpu().numpy()[0]
            print(f"     Action from actor: {action}")
            
            # Track portfolio before step
            if hasattr(env, 'state') and len(env.state) > 2:
                cash_before = env.state[0]
                portfolio_before = cash_before
                if len(env.state) > env.stock_dim * 2:
                    holdings_before = env.state[1:env.stock_dim+1]
                    prices_before = env.state[env.stock_dim+1:2*env.stock_dim+1]
                    portfolio_before = cash_before + np.sum(holdings_before * prices_before)
                print(f"     Portfolio before: ${portfolio_before:.2f}")
            
            # Take step
            state, reward, terminated, truncated, step_info = env.step(action)
            cumulative_returns += reward
            
            print(f"     Reward: {reward:.10f}")
            print(f"     Cumulative return: {cumulative_returns:.10f}")
            print(f"     Terminated: {terminated}, Truncated: {truncated}")
            
            step_details.append({
                'step': episode_steps + 1,
                'action': action,
                'reward': reward,
                'cumulative_return': cumulative_returns,
                'terminated': terminated,
                'truncated': truncated
            })
            
            if terminated or truncated:
                print(f"     Episode ended at step {episode_steps + 1}")
                break
        
        final_cumulative_returns = getattr(env, 'cumulative_returns', cumulative_returns)
        final_steps = episode_steps + 1
        
        print(f"\n📊 Episode Summary:")
        print(f"   Final cumulative returns: {final_cumulative_returns:.10f}")
        print(f"   Episode steps: {final_steps}")
        print(f"   Env cumulative_returns attribute: {getattr(env, 'cumulative_returns', 'Not set')}")
        
        print(f"\n📋 Step-by-step details:")
        for detail in step_details:
            print(f"     Step {detail['step']}: Action={detail['action'][0]:6.3f}, "
                  f"Reward={detail['reward']:10.8f}, Cumulative={detail['cumulative_return']:10.8f}")
        
        # Check if rewards are actually varying
        rewards = [detail['reward'] for detail in step_details]
        reward_std = np.std(rewards)
        
        print(f"\n🎯 Reward variation analysis:")
        print(f"   Individual step rewards: {rewards}")
        print(f"   Reward standard deviation: {reward_std:.10f}")
        
        if reward_std > 1e-12:
            print(f"✅ Individual step rewards vary")
        else:
            print(f"❌ Individual step rewards are constant")
            print(f"   This is the core issue - environment is not producing varying rewards")
        
        return reward_std > 1e-12
        
    except Exception as e:
        print(f"❌ Error in step-by-step debugging: {e}")
        traceback.print_exc()
        return False

def main():
    """Main debug function."""
    print("🔍 Focused ElegantRL Evaluator Debug Script")
    print("=" * 60)
    print("Testing the exact get_rewards_and_steps function and environment behavior")
    print("=" * 60)
    
    results = {}
    
    # Test 1: Core environment issue
    try:
        print("RUNNING TEST 1: Core Environment Issue Analysis")
        success1 = test_core_environment_issue()
        results['core_env_test'] = success1
    except Exception as e:
        print(f"❌ Test 1 failed: {e}")
        results['core_env_test'] = False
    
    # Test 2: ElegantRL get_rewards_and_steps function
    try:
        print("\nRUNNING TEST 2: ElegantRL get_rewards_and_steps Function")
        success2 = test_elegantrl_get_rewards_and_steps()
        results['elegantrl_function_test'] = success2
    except Exception as e:
        print(f"❌ Test 2 failed: {e}")
        results['elegantrl_function_test'] = False
    
    # Test 3: Step-by-step debugging
    try:
        print("\nRUNNING TEST 3: Step-by-step Debugging")
        success3 = test_step_by_step_debugging()
        results['step_by_step_test'] = success3
    except Exception as e:
        print(f"❌ Test 3 failed: {e}")
        results['step_by_step_test'] = False
    
    # Final analysis
    print("\n" + "="*60)
    print("🎯 FINAL DIAGNOSIS")
    print("="*60)
    
    print(f"\nTest Results:")
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    passing_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\nOverall: {passing_tests}/{total_tests} tests passed")
    
    if passing_tests == 0:
        print("\n❌ CRITICAL ISSUE IDENTIFIED")
        print("   The environment is not producing varying rewards at all.")
        print("   This is why ElegantRL evaluator shows constant avgR.")
        print("\n🔧 IMMEDIATE FIX NEEDED:")
        print("   1. Check AsymmetricTradingEnv.step() method")
        print("   2. Verify portfolio value calculation")
        print("   3. Ensure actions actually affect environment state")
        print("   4. Check reward calculation formula")
    elif results.get('core_env_test', False) and not results.get('elegantrl_function_test', False):
        print("\n⚠️  INTEGRATION ISSUE")
        print("   Environment works individually but fails with ElegantRL.")
        print("   Check environment-ElegantRL interface compatibility.")
    elif all(results.values()):
        print("\n🎉 ALL TESTS PASSED!")
        print("   Environment produces varying rewards.")
        print("   The constant avgR issue may be in:")
        print("   1. Training loop frequency")
        print("   2. Agent action selection during evaluation")
        print("   3. Environment reset between episodes")
    else:
        print("\n🔍 MIXED RESULTS")
        print("   Some tests pass, some fail. Check specific test outputs above.")
    
    return passing_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)