#!/usr/bin/env python3
"""
Test the index corruption fix with actual training scenario
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig
from models.sac_agent import SACAgent

print("🔍 Testing index fix with realistic training scenario...")

# Create realistic training data (simulate 200 days with 10 stocks like main.py)
test_data = []
symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'TSLA', 'AVGO', 'ADBE', 'ASML']

for i in range(200):  # 200 trading days
    for j, stock in enumerate(symbols):
        base_price = 100 + j*50  # Different base prices for different stocks
        price = base_price + i*0.5 + np.random.normal(0, 2)  # Add some randomness
        test_data.append({
            'tic': stock,
            'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': price, 'open': price*0.99, 'high': price*1.02, 'low': price*0.98, 
            'volume': 1000000 + np.random.randint(-100000, 100000),
            'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
            'ema_12': price, 'ema_26': price, 'rsi_14': 50.0 + np.random.normal(0, 10),
            'macd_12_26_9': np.random.normal(0, 0.5), 'macds_12_26_9': np.random.normal(0, 0.5),
            'macdh_12_26_9': np.random.normal(0, 0.5), 'cci_20': np.random.normal(0, 50),
            'adx_14': 30.0 + np.random.normal(0, 5), 'dmp_14': 25.0, 'dmn_14': 25.0,
            'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price, 'bbu_20_2.0': price*1.05,
            'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000, 'turbulence': 0.1,
            'price_range': 0.02, 'price_position': 0.5, 'returns_1d': 0.001,
            'returns_5d': 0.005, 'returns_20d': 0.02, 'volume_ma_20': 1000000,
            'volume_ratio': 1.0, 'volatility_20d': 0.02, 'vix_ma_5': 20.0,
            'vix_ma_20': 20.0, 'vix_percentile_252': 0.5, 'vix_change': 0.0,
            'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📊 Training data: {len(df)} rows, {len(df.index.unique())} days, {df['tic'].nunique()} stocks")
print(f"Date range: {df['date'].min()} to {df['date'].max()}")

try:
    # Create environment (this should trigger and fix the index corruption)
    env = AsymmetricTradingEnv(
        df=df,
        stock_dim=10,
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0] * 10,
        buy_cost_pct=[0.001] * 10,
        sell_cost_pct=[0.001] * 10,
        reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=symbols),
        log_level='INFO',
        tech_indicator_list=[
            'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 
            'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
            'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 
            'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 
            'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 
            'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 
            'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
        ]
    )
    
    print(f"✅ Environment created successfully!")
    print(f"Max steps: {env.max_step}")
    print(f"State dim: {env.observation_space.shape[0]}")
    print(f"Action dim: {env.action_space.shape[0]}")
    
    # Create SAC agent
    agent_config = {
        'learning_rate': 0.001,
        'gamma': 0.99,
        'tau': 0.005,
        'alpha': 0.2,
        'batch_size': 64,
        'buffer_size': 10000,
        'net_dims': [64, 64],
        'repeat_times': 1.0,
        'reward_scale': 1.0,
        'if_per': False,
        'if_off_policy': True,
        'checkpoint_dir': 'models/checkpoints'
    }
    
    sac_agent = SACAgent(
        state_dim=env.observation_space.shape[0],
        action_dim=env.action_space.shape[0],
        config=agent_config,
        asymmetric_config=AsymmetricConfig(symbols=symbols)
    )
    
    print(f"✅ SAC agent created")
    
    # Run very short training to test if avgS issue is resolved
    print(f"\n🏃 Running short training test...")
    
    results = sac_agent.train(
        env=env,
        total_timesteps=5000,  # Very short training
        eval_env=None,  # Skip evaluation for speed
        eval_freq=10000,  # No evaluation
        save_freq=10000,  # No saving
        model_dir='models/checkpoints'
    )
    
    print(f"✅ Training completed!")
    print(f"Results: {results}")
    
    # Check the recorder file for avgS
    try:
        recorder = np.load('models/checkpoints/recorder.npy')
        print(f"\n📊 Training Statistics:")
        print(f"  Raw recorder data: {recorder}")
        if recorder.shape[1] >= 3:
            avgR = recorder[0, 1] if recorder.shape[0] > 0 else 0
            avgS = recorder[0, 2] if recorder.shape[0] > 0 else 0
            print(f"  avgR: {avgR:.8f}")
            print(f"  avgS: {avgS:.1f}")
            
            if avgS == 0:
                print(f"❌ avgS is still 0 - issue not fully resolved")
            else:
                print(f"✅ avgS shows proper value - index fix successful!")
                
    except Exception as e:
        print(f"Could not read recorder: {e}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n📋 Summary:")
print(f"This test verifies if the index corruption fix resolves the avgS=0 issue")
print(f"that was preventing proper ElegantRL training statistics.")