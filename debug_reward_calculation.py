#!/usr/bin/env python3
"""
Debug script to test reward calculation in AsymmetricTradingEnv
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔍 Testing AsymmetricTradingEnv reward calculation...")

# Create test data
test_data = []
base_prices = [100, 105, 102, 108, 95, 98, 103, 110, 107, 112]  # Price variation to test rewards

for i, price in enumerate(base_prices):
    for stock in ['AAPL', 'MSFT']:
        test_data.append({
            'tic': stock,
            'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': price + (5 if stock == 'MSFT' else 0),  # MSFT slightly higher
            'open': price + (5 if stock == 'MSFT' else 0),
            'high': price + 2 + (5 if stock == 'MSFT' else 0),
            'low': price - 2 + (5 if stock == 'MSFT' else 0),
            'volume': 1000000,
            # Add technical indicators
            'sma_5': price,
            'sma_10': price,
            'sma_20': price,
            'rsi_14': 50.0,
            'macd_12_26_9': 0.0,
            'ema_12': price,
            'ema_26': price,
            'cci_20': 0.0,
            'adx_14': 30.0,
            'bbl_20_2.0': price - 5,
            'bbm_20_2.0': price,
            'bbu_20_2.0': price + 5,
            'obv': 1000000,
            'turbulence': 0.1
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📊 Created test data: {len(df)} rows, {len(df.index.unique())} days, {df['tic'].nunique()} stocks")
print(f"💰 Price range: ${df['close'].min():.2f} - ${df['close'].max():.2f}")

# Create environment
try:
    env = AsymmetricTradingEnv(
        df=df,
        stock_dim=2,  # AAPL and MSFT
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0, 0],
        buy_cost_pct=[0.001, 0.001],  # 0.1% transaction cost
        sell_cost_pct=[0.001, 0.001],
        reward_scaling=1e-4,  # Standard FinRL reward scaling
        asymmetric_config=AsymmetricConfig(symbols=['AAPL', 'MSFT']),
        log_level='INFO',  # Reduce logging for cleaner output
        tech_indicator_list=['sma_5', 'sma_10', 'sma_20', 'rsi_14', 'macd_12_26_9', 'ema_12', 'ema_26', 'cci_20', 'adx_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'obv', 'turbulence']
    )
    
    print(f"✅ Environment created successfully")
    print(f"🎯 Max steps: {env.max_step}")
    print(f"📈 State space: {env.observation_space.shape}")
    print(f"🎮 Action space: {env.action_space.shape}")
    
    # Reset environment
    state, info = env.reset()
    print(f"\n🔄 Environment reset")
    print(f"💵 Initial portfolio value: ${env.asset_memory[0]:,.2f}")
    
    # Test different action scenarios
    test_scenarios = [
        ("No action", [0.0, 0.0]),
        ("Small buy AAPL", [0.1, 0.0]),  # 10% action strength
        ("Large buy AAPL", [0.8, 0.0]),  # 80% action strength
        ("Buy both stocks", [0.3, 0.3]),  # 30% each
        ("Sell AAPL (if holding)", [-0.5, 0.0]),  # 50% sell
    ]
    
    for scenario_name, actions in test_scenarios:
        print(f"\n🧪 Testing scenario: {scenario_name}")
        print(f"🎮 Actions: {actions}")
        
        # Execute step
        try:
            state, reward, terminated, truncated, info = env.step(np.array(actions))
            
            # Log detailed results
            portfolio_value = info.get('total_asset', 0)
            cash = info.get('cash', 0)
            holdings = info.get('holdings', [])
            prices = info.get('prices', [])
            
            print(f"💰 Reward: {reward:.8f}")
            print(f"💵 Portfolio value: ${portfolio_value:,.2f}")
            print(f"💸 Cash: ${cash:,.2f}")
            print(f"📊 Holdings: {holdings}")
            print(f"💲 Prices: {prices}")
            
            if hasattr(env, 'asset_memory') and len(env.asset_memory) > 1:
                portfolio_change = env.asset_memory[-1] - env.asset_memory[-2]
                print(f"📈 Portfolio change: ${portfolio_change:,.2f}")
                
            if terminated:
                print("🛑 Episode terminated")
                break
                
        except Exception as e:
            print(f"❌ Error in step: {e}")
            import traceback
            traceback.print_exc()
            break
    
    print(f"\n📊 Final Results:")
    print(f"💵 Final portfolio: ${env.asset_memory[-1]:,.2f}")
    print(f"📈 Total return: {((env.asset_memory[-1] / env.asset_memory[0]) - 1) * 100:.2f}%")
    print(f"🏦 Asset memory length: {len(env.asset_memory)}")
    
except Exception as e:
    print(f"❌ Error creating environment: {e}")
    import traceback
    traceback.print_exc()