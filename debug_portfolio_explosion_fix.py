#!/usr/bin/env python3
"""
Debug script to test portfolio explosion fix.
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig
from config.settings import Settings

def test_portfolio_explosion_fix():
    """Test if the portfolio explosion fix works."""
    print("Testing portfolio explosion fix...")
    
    # Load settings and create minimal test data
    settings = Settings()
    
    # Create minimal test data with just 2 days and 2 stocks
    test_data = {
        'date': ['2023-01-01', '2023-01-02'] * 2,
        'tic': ['AAPL', 'AAPL', 'MSFT', 'MSFT'],
        'close': [150.0, 151.0, 250.0, 251.0],
        'open': [149.0, 150.5, 249.0, 250.5],
        'high': [152.0, 153.0, 252.0, 253.0],
        'low': [148.0, 149.0, 248.0, 249.0],
        'volume': [1000000, 1100000, 800000, 850000],
        'sma_5': [150.0, 151.0, 250.0, 251.0],
        'rsi_14': [50.0, 55.0, 45.0, 50.0]
    }
    
    df = pd.DataFrame(test_data)
    df['date'] = pd.to_datetime(df['date'])
    df['day'] = df['date'].factorize()[0]
    df = df.set_index('day')
    
    print(f"Test data shape: {df.shape}")
    print(f"Test data:\n{df}")
    
    # Create asymmetric config
    asymmetric_config = AsymmetricConfig(
        symbols=['AAPL', 'MSFT'],
        target_upside_downside_ratio=2.0,
        momentum_threshold=0.02,
        mean_reversion_threshold=0.05,
        volatility_lookback=20,
        rsi_period=14,
        rsi_oversold=30,
        rsi_overbought=70,
        fast_ma_period=5,
        slow_ma_period=20,
        bb_period=20,
        bb_std=2.0,
        signal_threshold=0.6
    )
    
    # Create environment with explosion-prone settings
    env_config = {
        'df': df,
        'stock_dim': 2,
        'hmax': 1000,
        'initial_amount': 100000,  # Start with $100K
        'num_stock_shares': [0, 0],
        'buy_cost_pct': [0.001, 0.001],
        'sell_cost_pct': [0.001, 0.001],
        'state_space': 50,
        'action_space': 2,
        'tech_indicator_list': ['sma_5', 'rsi_14'],
        'reward_scaling': 1e-4,
        'asymmetric_config': asymmetric_config,
        'enhanced_state_input': True,
        'day': 0,
        'initial': True,
        'previous_state': None,
        'model_name': 'test',
        'mode': 'test',
        'iteration': 'test'
    }
    
    env = AsymmetricTradingEnv(**env_config)
    state, info = env.reset()  # reset() returns tuple (state, info)
    
    print(f"Initial state shape: {state.shape}")
    print(f"Initial cash: ${env.state[0]:.2f}")
    print(f"Initial holdings: {env.state[1:3]}")
    
    # Test with increasingly large actions that would cause explosion
    test_actions_list = [
        np.array([0.5, 0.3]),      # Normal actions
        np.array([1.0, 1.0]),      # Max normal actions  
        np.array([2.0, 1.5]),      # Slightly large actions
        np.array([5.0, 3.0]),      # Large actions that might cause explosion
        np.array([10.0, 8.0])      # Very large actions that would definitely cause explosion
    ]
    
    for i, actions in enumerate(test_actions_list):
        print(f"\n--- Test {i+1}: Actions = {actions} ---")
        
        try:
            # Reset environment for each test
            state, info = env.reset()
            initial_portfolio = env.state[0] + np.sum(env.state[1:3] * env.state[3:5])
            print(f"Initial portfolio value: ${initial_portfolio:.2f}")
            
            # Take action
            next_state, reward, done, truncated, info = env.step(actions)
            final_portfolio = env.state[0] + np.sum(env.state[1:3] * env.state[3:5])
            
            print(f"Final cash: ${env.state[0]:.2f}")
            print(f"Final holdings: {env.state[1:3]}")
            print(f"Final portfolio value: ${final_portfolio:.2f}")
            print(f"Portfolio change: ${final_portfolio - initial_portfolio:.2f}")
            
            # Check if portfolio exploded
            if final_portfolio > 1000000:  # More than $1M indicates explosion
                print(f"❌ PORTFOLIO EXPLOSION DETECTED: ${final_portfolio:.2e}")
            else:
                print(f"✅ Portfolio remained reasonable: ${final_portfolio:.2f}")
                
        except Exception as e:
            print(f"❌ Error during test: {e}")
    
    print("\n=== Portfolio Explosion Fix Test Complete ===")

if __name__ == '__main__':
    test_portfolio_explosion_fix()