#!/usr/bin/env python3
"""Debug the environment initialization to see what DataFrame it actually receives"""

import sys
sys.path.insert(0, 'src')
import pandas as pd

print("🔍 Testing AsymmetricTradingEnv initialization with correct data")

try:
    # Load the exact same data that main.py uses
    df = pd.read_csv('data/processed/processed_data.csv')
    
    # Apply exact same transformation
    df['date'] = pd.to_datetime(df['date'])
    train_df = df[
        (df['date'] >= '2016-01-01') & 
        (df['date'] <= '2022-12-31')
    ].reset_index(drop=True)
    
    train_df = train_df.sort_values(['date', 'tic']).reset_index(drop=True)
    train_df['day'] = train_df['date'].factorize()[0]
    train_df_final = train_df.set_index('day')
    
    print(f"✅ Prepared data: {len(train_df_final.index.unique())} unique days")
    
    # Now create AsymmetricTradingEnv with this exact data
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    
    asymmetric_config = AsymmetricConfig(target_upside_downside_ratio=2.0)
    
    # Create environment with minimal config (like main.py)
    env_config = {
        'df': train_df_final,
        'stock_dim': 10,
        'hmax': 100,
        'initial_amount': 100000,
        'num_stock_shares': [0] * 10,
        'buy_cost_pct': [0.001] * 10,
        'sell_cost_pct': [0.001] * 10,
        'reward_scaling': 0.0001,
        'asymmetric_config': asymmetric_config,
        'log_level': 'INFO',
        'day': 0,
        'initial': True,
        'previous_state': None,
        'model_name': 'test',
        'mode': 'train',
        'iteration': 'test'
    }
    
    print(f"🏗️ Creating AsymmetricTradingEnv...")
    
    # This should trigger our debug logs in the environment
    env = AsymmetricTradingEnv(**env_config)
    
    print(f"✅ Environment created successfully!")
    
    # Test the termination logic directly
    print(f"\n🧪 Testing termination logic:")
    print(f"env.df.index.unique() length: {len(env.df.index.unique())}")
    print(f"Environment will terminate at day: {len(env.df.index.unique()) - 1}")
    
    # Test a few steps
    print(f"\n🎮 Testing environment steps:")
    state = env.reset()
    print(f"Initial state shape: {state[0].shape if isinstance(state, tuple) else state.shape}")
    
    for step in range(5):
        action = [0.1] * 10  # Small buy action
        result = env.step(action)
        state, reward, terminated, truncated, info = result
        
        print(f"Step {step}: day={env.day}, terminated={terminated}, reward={reward:.8f}")
        
        if terminated:
            print(f"❌ Environment terminated early at step {step}!")
            break
    
    # Test jumping to day 123
    print(f"\n🎯 Testing jump to day 123:")
    env.day = 123
    try:
        action = [0.0] * 10
        result = env.step(action)
        state, reward, terminated, truncated, info = result
        print(f"Day 123: terminated={terminated}, day_after_step={env.day}")
    except Exception as e:
        print(f"❌ Error at day 123: {e}")
    
    # Test jumping to day 124
    print(f"\n🎯 Testing jump to day 124:")
    env.day = 124
    try:
        action = [0.0] * 10
        result = env.step(action)
        state, reward, terminated, truncated, info = result
        print(f"Day 124: terminated={terminated}, day_after_step={env.day}")
    except Exception as e:
        print(f"❌ Error at day 124: {e}")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()