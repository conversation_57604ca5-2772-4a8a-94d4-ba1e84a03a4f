#!/usr/bin/env python3
"""
Test profitable trading scenario to verify positive rewards work
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🎯 Testing profitable trading scenario...")

# Create trending upward data for profitable trading
test_data = []
prices = [100, 101, 103, 106, 110, 115, 121, 128, 136, 145]  # Strong upward trend

for i, price in enumerate(prices):
    for stock in ['AAPL', 'MSFT']:
        test_data.append({
            'tic': stock,
            'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': price + (2 if stock == 'MSFT' else 0),
            'open': price + (2 if stock == 'MSFT' else 0),
            'high': price + 1 + (2 if stock == 'MSFT' else 0),
            'low': price - 1 + (2 if stock == 'MSFT' else 0),
            'volume': 1000000,
            'sma_5': price, 'sma_10': price, 'sma_20': price, 'rsi_14': 60.0,
            'macd_12_26_9': 1.0, 'ema_12': price, 'ema_26': price, 'cci_20': 10.0,
            'adx_14': 40.0, 'bbl_20_2.0': price - 3, 'bbm_20_2.0': price,
            'bbu_20_2.0': price + 3, 'obv': 1000000, 'turbulence': 0.05
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📈 Upward trending data: ${df['close'].min():.0f} → ${df['close'].max():.0f} (+{((df['close'].max()/df['close'].min()) - 1)*100:.1f}%)")

# Create environment
env = AsymmetricTradingEnv(
    df=df,
    stock_dim=2,
    hmax=1000,  # Allow more shares
    initial_amount=100000,
    num_stock_shares=[0, 0],
    buy_cost_pct=[0.001, 0.001],
    sell_cost_pct=[0.001, 0.001],
    reward_scaling=1e-4,
    asymmetric_config=AsymmetricConfig(symbols=['AAPL', 'MSFT']),
    log_level='ERROR',  # Reduce noise
    tech_indicator_list=['sma_5', 'sma_10', 'sma_20', 'rsi_14', 'macd_12_26_9', 'ema_12', 'ema_26', 'cci_20', 'adx_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'obv', 'turbulence']
)

state, info = env.reset()
print(f"💰 Starting portfolio: ${env.asset_memory[0]:,.2f}")

# Strategy: Buy early and hold, sell at the end
profitable_actions = [
    [0.8, 0.8],   # Day 1: Buy both stocks heavily (80% action strength)
    [0.0, 0.0],   # Day 2: Hold
    [0.0, 0.0],   # Day 3: Hold
    [0.0, 0.0],   # Day 4: Hold
    [0.0, 0.0],   # Day 5: Hold
    [0.0, 0.0],   # Day 6: Hold
    [0.0, 0.0],   # Day 7: Hold
    [0.0, 0.0],   # Day 8: Hold
    [-1.0, -1.0], # Day 9: Sell everything (100% sell signal)
]

total_reward = 0.0
for day, actions in enumerate(profitable_actions):
    if env.day >= env.max_step - 1:
        break
        
    state, reward, terminated, truncated, info = env.step(np.array(actions))
    total_reward += reward
    
    portfolio_value = info.get('total_asset', 0)
    cash = info.get('cash', 0)
    holdings = info.get('holdings', [])
    
    action_desc = "BUY HEAVY" if actions[0] > 0.5 else "SELL ALL" if actions[0] < -0.5 else "HOLD"
    holdings_str = f"[{holdings[0]:6.1f}, {holdings[1]:6.1f}]" if len(holdings) >= 2 else str(holdings)
    print(f"Day {env.day-1}: {action_desc:10} | Reward: {reward:+.8f} | Portfolio: ${portfolio_value:8,.0f} | Cash: ${cash:8,.0f} | Holdings: {holdings_str}")
    
    if terminated:
        break

final_portfolio = env.asset_memory[-1]
total_return = ((final_portfolio / env.asset_memory[0]) - 1) * 100

print(f"\n🎉 RESULTS:")
print(f"💰 Final portfolio: ${final_portfolio:,.2f}")
print(f"📈 Total return: {total_return:+.2f}%")
print(f"🏆 Total cumulative reward: {total_reward:+.8f}")
print(f"⚡ Average reward per step: {total_reward / len(profitable_actions):+.8f}")

if total_reward > 0:
    print("✅ SUCCESS: Agent can achieve positive rewards with profitable trading!")
else:
    print("❌ Issue: Even profitable trading yielded negative total reward")
    
if total_return > 5:
    print("✅ SUCCESS: Strategy was profitable (>5% return)")
else:
    print("❌ Issue: Strategy was not sufficiently profitable")