#!/usr/bin/env python3
"""Test script to verify state space dimension fix."""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config.settings import Settings
from backtesting.engine import BacktestEngine
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

def test_state_space_calculation():
    """Test that state space calculation is consistent between training and backtesting."""
    
    print("Testing state space dimension consistency...")
    
    # Load settings
    settings = Settings()
    
    # Create sample data similar to real data
    dates = pd.date_range(start='2023-01-01', end='2023-01-10', freq='D')
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    
    data_rows = []
    for date in dates:
        for symbol in symbols:
            row = {
                'date': date,
                'tic': symbol,
                'open': 100.0,
                'high': 102.0,
                'low': 98.0,
                'close': 101.0,
                'volume': 1000000,
            }
            # Add technical indicators from settings
            for tech_indicator in settings.data.tech_indicator_list:
                row[tech_indicator.lower()] = np.random.random()
            
            data_rows.append(row)
    
    df = pd.DataFrame(data_rows)
    print(f"Created test data: {len(df)} rows, {len(symbols)} symbols")
    print(f"Technical indicators: {len(settings.data.tech_indicator_list)}")
    
    # Test 1: BacktestEngine state space calculation
    print("\n=== Testing BacktestEngine state space calculation ===")
    
    backtest_engine = BacktestEngine(settings)
    
    # Test the _create_backtest_environment method to see state space calculation
    try:
        env = backtest_engine._create_backtest_environment(df)
        
        print(f"BacktestEngine results:")
        print(f"  stock_dim: {env.stock_dim}")
        print(f"  state_space: {env.state_space}")
        print(f"  enhanced_state_space: {getattr(env, 'enhanced_state_space', 'N/A')}")
        print(f"  observation_space.shape: {env.observation_space.shape}")
        print(f"  action_space.shape: {env.action_space.shape}")
        
        # Test getting a state
        reset_result = env.reset()
        if isinstance(reset_result, tuple):
            state, _ = reset_result
        else:
            state = reset_result
        
        print(f"  actual state shape: {state.shape}")
        print(f"  state size match: {state.shape[0] == env.observation_space.shape[0]}")
        
        if state.shape[0] != env.observation_space.shape[0]:
            print(f"  ERROR: State size mismatch!")
            print(f"    Expected: {env.observation_space.shape[0]}")
            print(f"    Actual: {state.shape[0]}")
            return False
        else:
            print(f"  SUCCESS: State dimensions are consistent!")
        
    except Exception as e:
        print(f"  ERROR in BacktestEngine: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 2: Compare with training-style calculation
    print("\n=== Comparing with training-style calculation ===")
    
    stock_dim = len(df['tic'].unique())
    tech_indicators_count = len(settings.data.tech_indicator_list)
    
    # Training calculation (from main.py)
    base_state_space_training = 1 + 2 * stock_dim + len(settings.data.tech_indicator_list) * stock_dim
    asymmetric_features_training = 5 * stock_dim  # Training uses 5 features per stock
    enhanced_state_space_training = base_state_space_training + asymmetric_features_training
    
    # BacktestEngine calculation (from engine.py)
    base_state_space_backtest = 1 + 2 * stock_dim + (stock_dim * tech_indicators_count)
    asymmetric_features_backtest = 3 * stock_dim  # AsymmetricTradingEnv actually uses 3 features per stock
    enhanced_state_space_backtest = base_state_space_backtest + asymmetric_features_backtest
    
    print(f"Training calculation:")
    print(f"  base_state_space: {base_state_space_training}")
    print(f"  asymmetric_features: {asymmetric_features_training} (5 per stock)")
    print(f"  enhanced_state_space: {enhanced_state_space_training}")
    
    print(f"Backtest calculation:")
    print(f"  base_state_space: {base_state_space_backtest}")
    print(f"  asymmetric_features: {asymmetric_features_backtest} (3 per stock)")
    print(f"  enhanced_state_space: {enhanced_state_space_backtest}")
    
    print(f"Environment reports:")
    print(f"  env.state_space: {env.state_space}")
    print(f"  env.enhanced_state_space: {getattr(env, 'enhanced_state_space', 'N/A')}")
    
    # The key insight: training assumes 5 asymmetric features per stock, 
    # but AsymmetricTradingEnv._get_asymmetric_state_features actually provides 3
    if env.state_space == enhanced_state_space_backtest:
        print(f"  SUCCESS: BacktestEngine calculation matches environment!")
    else:
        print(f"  WARNING: Calculation mismatch detected")
        print(f"    Expected (backtest): {enhanced_state_space_backtest}")
        print(f"    Actual (env): {env.state_space}")
    
    return True

if __name__ == "__main__":
    success = test_state_space_calculation()
    if success:
        print("\n✅ State space dimension test completed successfully!")
    else:
        print("\n❌ State space dimension test failed!")
        sys.exit(1)