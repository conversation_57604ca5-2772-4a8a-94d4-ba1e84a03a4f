#!/usr/bin/env python3
"""Fix the missing max_step attribute in AsymmetricTradingEnv"""

import sys
sys.path.insert(0, 'src')

print("🔧 Adding max_step attribute to AsymmetricTradingEnv")

# Read the current file
with open('src/trading/asymmetric_env.py', 'r') as f:
    content = f.read()

# Find where to add max_step attribute
# Look for the line where we log "AsymmetricTradingEnv initialized"
if 'self.max_step =' in content:
    print("✅ max_step attribute already exists")
else:
    print("❌ max_step attribute missing - adding it...")
    
    # Find the right place to add it (after df initialization)
    target_line = 'self.logger.info(f"AsymmetricTradingEnv initialized with {stock_dim} stocks")'
    
    if target_line in content:
        # Add max_step right before this line
        max_step_code = """        
        # Add max_step attribute for ElegantRL compatibility
        self.max_step = len(self.df.index.unique())
        self.logger.info(f"🔧 Set max_step = {self.max_step} for ElegantRL compatibility")
        """
        
        # Insert the code
        content = content.replace(target_line, max_step_code + target_line)
        
        # Write the updated file
        with open('src/trading/asymmetric_env.py', 'w') as f:
            f.write(content)
            
        print("✅ Added max_step attribute to AsymmetricTradingEnv")
        print("   max_step will be set to len(df.index.unique())")
        
    else:
        print("❌ Could not find insertion point")

print("\n🧪 Testing the fix...")

try:
    # Test that the fix works
    import pandas as pd
    df = pd.read_csv('data/processed/processed_data.csv')
    df['date'] = pd.to_datetime(df['date'])
    train_df = df[
        (df['date'] >= '2016-01-01') & 
        (df['date'] <= '2022-12-31')
    ].reset_index(drop=True)
    
    train_df = train_df.sort_values(['date', 'tic']).reset_index(drop=True)
    train_df['day'] = train_df['date'].factorize()[0]
    train_df_final = train_df.set_index('day')
    
    # Import the fixed environment
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    
    asymmetric_config = AsymmetricConfig(target_upside_downside_ratio=2.0)
    
    env_config = {
        'df': train_df_final,
        'stock_dim': 10,
        'hmax': 100,
        'initial_amount': 100000,
        'num_stock_shares': [0] * 10,
        'buy_cost_pct': [0.001] * 10,
        'sell_cost_pct': [0.001] * 10,
        'reward_scaling': 0.0001,
        'asymmetric_config': asymmetric_config,
        'log_level': 'ERROR',
        'day': 0,
        'initial': True,
        'previous_state': None,
        'model_name': 'test',
        'mode': 'train',
        'iteration': 'test'
    }
    
    env = AsymmetricTradingEnv(**env_config)
    
    if hasattr(env, 'max_step'):
        print(f"✅ Environment now has max_step = {env.max_step}")
        
        # Test with ElegantRL
        from elegantrl.train.evaluator import get_rewards_and_steps
        import torch
        
        class DummyActor(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.dummy = torch.nn.Parameter(torch.tensor([0.0]))
                
            def forward(self, state):
                batch_size = state.shape[0] if len(state.shape) > 1 else 1
                return torch.zeros((batch_size, 10))
        
        actor = DummyActor()
        
        print("🧪 Testing ElegantRL get_rewards_and_steps with fix...")
        cumulative_returns, episode_steps = get_rewards_and_steps(env, actor)
        
        print(f"✅ SUCCESS!")
        print(f"   Episode steps: {episode_steps}")
        print(f"   Cumulative returns: {cumulative_returns}")
        
        if episode_steps > 1500:
            print(f"🎉 FIXED! Episodes now run {episode_steps} steps instead of 123!")
        else:
            print(f"❌ Still broken - only {episode_steps} steps")
            
    else:
        print("❌ max_step attribute still missing")
        
except Exception as e:
    print(f"❌ Error testing fix: {e}")
    import traceback
    traceback.print_exc()