#!/usr/bin/env python3
"""
Debug pyfolio tear sheet generation.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def test_pyfolio():
    """Test pyfolio with minimal data."""
    print("Testing pyfolio tear sheet generation...")
    
    try:
        import pyfolio as pf
        print(f"✅ Pyfolio imported successfully. Version: {getattr(pf, '__version__', 'unknown')}")
    except ImportError as e:
        print(f"❌ Failed to import pyfolio: {e}")
        return
    
    # Create minimal test returns data similar to what our backtest generates
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    # Generate realistic daily returns (small values like our backtest)
    returns_data = np.random.normal(0.0005, 0.02, len(dates))  # ~0.05% daily return with 2% volatility
    
    returns = pd.Series(returns_data, index=dates, name='returns')
    
    print(f"Created test returns:")
    print(f"  Type: {type(returns)}")
    print(f"  Shape: {returns.shape}")
    print(f"  Index type: {type(returns.index)}")
    print(f"  Data type: {returns.dtype}")
    print(f"  First few values: {returns.head().tolist()}")
    print(f"  Has NaN: {returns.isna().any()}")
    print(f"  Has Inf: {np.isinf(returns).any()}")
    print(f"  Min/Max: {returns.min():.6f} / {returns.max():.6f}")
    
    # Test the exact same cleaning process as our backtest
    print(f"\nCleaning returns data...")
    clean_returns = returns.dropna()
    clean_returns = clean_returns.replace([np.inf, -np.inf], np.nan).dropna()
    
    print(f"After cleaning:")
    print(f"  Type: {type(clean_returns)}")
    print(f"  Shape: {clean_returns.shape}")
    print(f"  Index type: {type(clean_returns.index)}")
    
    # Try to create pyfolio tear sheet
    try:
        print(f"\nTesting pyfolio.create_returns_tear_sheet...")
        fig = pf.create_returns_tear_sheet(
            returns=clean_returns,
            return_fig=True,
            live_start_date=None
        )
        print(f"✅ Pyfolio tear sheet created successfully!")
        if fig:
            print(f"  Figure type: {type(fig)}")
        return True
        
    except Exception as e:
        print(f"❌ Pyfolio tear sheet failed: {e}")
        print(f"  Error type: {type(e)}")
        
        # Try with different data formats
        print(f"\nTrying alternative formats...")
        
        # Test 1: Force to numpy array
        try:
            print("  Test 1: Converting to numpy array...")
            clean_array = clean_returns.values
            fig = pf.create_returns_tear_sheet(
                returns=clean_array,
                return_fig=True
            )
            print("  ✅ Numpy array worked!")
            return True
        except Exception as e1:
            print(f"  ❌ Numpy array failed: {e1}")
        
        # Test 2: Force frequency
        try:
            print("  Test 2: Setting frequency...")
            clean_returns_freq = clean_returns.copy()
            clean_returns_freq.index.freq = 'D'
            fig = pf.create_returns_tear_sheet(
                returns=clean_returns_freq,
                return_fig=True
            )
            print("  ✅ Setting frequency worked!")
            return True
        except Exception as e2:
            print(f"  ❌ Setting frequency failed: {e2}")
        
        # Test 3: Different pyfolio function
        try:
            print("  Test 3: Using different pyfolio function...")
            import pyfolio.timeseries as ts
            stats = ts.perf_stats(clean_returns)
            print(f"  ✅ pyfolio.timeseries.perf_stats worked! Stats: {type(stats)}")
            return True
        except Exception as e3:
            print(f"  ❌ pyfolio.timeseries.perf_stats failed: {e3}")
        
        return False

if __name__ == "__main__":
    test_pyfolio()