#!/usr/bin/env python3
"""
Debug script for ElegantRL evaluator constant avgR issue.

This script creates a minimal reproduction of the training setup to test:
1. Individual environment episode rewards
2. ElegantRL evaluator setup and reward collection
3. Reward calculation methods and variations
4. Environment state transitions and portfolio changes

The goal is to identify why ElegantRL shows constant avgR (-0.00) 
even when individual episodes should have varying rewards.
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any
import traceback
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def create_minimal_test_data(num_days: int = 50, num_stocks: int = 1) -> pd.DataFrame:
    """Create minimal test data with varying prices to ensure reward variation.
    
    Args:
        num_days: Number of trading days
        num_stocks: Number of stocks
        
    Returns:
        DataFrame with test market data
    """
    print(f"Creating test data: {num_days} days, {num_stocks} stocks")
    
    dates = pd.date_range('2023-01-01', periods=num_days, freq='D')
    tickers = [f'STOCK_{i}' for i in range(num_stocks)]
    
    data = []
    
    for i, date in enumerate(dates):
        for j, ticker in enumerate(tickers):
            # Create varying price patterns to ensure different rewards
            base_price = 100 + j * 10  # Different base prices per stock
            trend = np.sin(i * 0.2) * 10  # Sinusoidal trend
            noise = np.random.normal(0, 2)  # Random noise
            price = base_price + trend + noise
            
            # Ensure positive prices
            price = max(price, 50)
            
            data.append({
                'date': date,
                'tic': ticker,
                'open': price * 0.995,
                'high': price * 1.005,
                'low': price * 0.98,
                'close': price,
                'volume': np.random.randint(100000, 1000000),
                # Technical indicators (simplified)
                'sma_5': price * (1 + np.random.normal(0, 0.01)),
                'sma_10': price * (1 + np.random.normal(0, 0.02)),
                'ema_12': price * (1 + np.random.normal(0, 0.015)),
                'rsi_14': 30 + np.random.uniform(0, 40),
                'macd_12_26_9': np.random.normal(0, 0.5),
                'adx_14': 20 + np.random.uniform(0, 30),
                'bbl_20_2.0': price * 0.95,
                'bbm_20_2.0': price,
                'bbu_20_2.0': price * 1.05,
                'obv': np.random.randint(1000000, 10000000),
                'vix_ma_5': 15 + np.random.uniform(0, 10),
                'turbulence': np.random.uniform(0, 2)
            })
    
    df = pd.DataFrame(data)
    
    # Prepare data for FinRL (similar to train_asymmetric.py)
    df['date'] = pd.to_datetime(df['date'])
    df['day'] = df['date'].factorize()[0]
    df = df.set_index('day')
    
    print(f"✅ Created test data: {len(df)} rows, {len(df.columns)} columns")
    print(f"Date range: {df['date'].min()} to {df['date'].max()}")
    print(f"Tickers: {df['tic'].unique()}")
    
    return df

def test_individual_environment_rewards():
    """Test that individual AsymmetricTradingEnv episodes return varying rewards."""
    print("\n" + "="*60)
    print("🧪 TEST 1: Individual Environment Reward Variation")
    print("="*60)
    
    try:
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        
        # Create test data
        test_data = create_minimal_test_data(num_days=20, num_stocks=1)
        
        # Create asymmetric config
        asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            volatility_lookback=5
        )
        
        # Create environment with detailed logging
        env = AsymmetricTradingEnv(
            df=test_data,
            stock_dim=1,
            hmax=100,
            initial_amount=100000,
            num_stock_shares=[0],
            buy_cost_pct=[0.001],
            sell_cost_pct=[0.001],
            reward_scaling=1e-4,
            tech_indicator_list=['sma_5', 'ema_12', 'rsi_14', 'macd_12_26_9', 'adx_14'],
            asymmetric_config=asymmetric_config,
            log_level="ERROR"  # Reduce noise for debug output
        )
        
        print(f"✅ Environment created")
        print(f"   State space: {env.observation_space.shape}")
        print(f"   Action space: {env.action_space.shape}")
        
        # Run multiple episodes with different action strategies
        episode_rewards = []
        episode_details = []
        
        action_strategies = [
            ("conservative", lambda step: np.array([0.1 * np.sin(step * 0.1)])),
            ("aggressive", lambda step: np.array([0.5 * np.cos(step * 0.2)])),
            ("random", lambda step: np.array([np.random.uniform(-0.3, 0.3)])),
            ("buy_hold", lambda step: np.array([0.2 if step < 5 else 0.0])),
            ("mean_revert", lambda step: np.array([-0.1 * np.sin(step * 0.3)]))
        ]
        
        for strategy_name, action_func in action_strategies:
            print(f"\n📊 Running episode with {strategy_name} strategy:")
            
            state, info = env.reset()
            total_reward = 0
            step_rewards = []
            step_count = 0
            portfolio_values = []
            
            done = False
            while not done and step_count < 15:  # Limit steps for debug
                action = action_func(step_count)
                next_state, reward, terminated, truncated, step_info = env.step(action)
                
                total_reward += reward
                step_rewards.append(reward)
                step_count += 1
                
                # Extract portfolio value from info
                portfolio_value = step_info.get('total_asset', 0)
                portfolio_values.append(portfolio_value)
                
                print(f"   Step {step_count:2d}: Action={action[0]:6.3f}, "
                      f"Reward={reward:8.6f}, Portfolio=${portfolio_value:8.0f}")
                
                done = terminated or truncated
                state = next_state
            
            episode_rewards.append(total_reward)
            episode_details.append({
                'strategy': strategy_name,
                'total_reward': total_reward,
                'step_rewards': step_rewards,
                'portfolio_values': portfolio_values,
                'steps': step_count
            })
            
            print(f"   Episode Total: {total_reward:8.6f} (over {step_count} steps)")
        
        # Analyze reward variation
        episode_rewards = np.array(episode_rewards)
        reward_std = np.std(episode_rewards)
        reward_mean = np.mean(episode_rewards)
        
        print(f"\n📈 Episode Reward Analysis:")
        print(f"   Episode rewards: {episode_rewards}")
        print(f"   Mean: {reward_mean:8.6f}")
        print(f"   Std:  {reward_std:8.6f}")
        print(f"   Range: [{np.min(episode_rewards):8.6f}, {np.max(episode_rewards):8.6f}]")
        
        # Check individual step reward variation
        all_step_rewards = []
        for detail in episode_details:
            all_step_rewards.extend(detail['step_rewards'])
        
        step_reward_std = np.std(all_step_rewards)
        print(f"   Step reward std: {step_reward_std:8.6f}")
        
        success = reward_std > 1e-8 or step_reward_std > 1e-8
        print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}: "
              f"{'Rewards vary between episodes' if success else 'Rewards are constant'}")
        
        return success, episode_details
        
    except Exception as e:
        print(f"❌ Error in environment test: {e}")
        traceback.print_exc()
        return False, []

def test_elegantrl_evaluator_setup():
    """Test how ElegantRL evaluator is set up and how it collects rewards."""
    print("\n" + "="*60)
    print("🧪 TEST 2: ElegantRL Evaluator Setup")
    print("="*60)
    
    try:
        # Import ElegantRL components
        from elegantrl.agent import AgentSAC
        from elegantrl.env import build_env
        from elegantrl.run import train_and_evaluate, get_gym_env_args
        
        print("✅ ElegantRL imports successful")
        
        # Try to understand how ElegantRL expects the environment
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        
        # Create test environment
        test_data = create_minimal_test_data(num_days=20, num_stocks=1)
        
        asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            volatility_lookback=5
        )
        
        env = AsymmetricTradingEnv(
            df=test_data,
            stock_dim=1,
            hmax=100,
            initial_amount=100000,
            num_stock_shares=[0],
            buy_cost_pct=[0.001],
            sell_cost_pct=[0.001],
            reward_scaling=1e-4,
            tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
            asymmetric_config=asymmetric_config,
            log_level="ERROR"
        )
        
        # Test the get_gym_env_args function which ElegantRL uses
        print("\n🔍 Testing ElegantRL environment wrapper:")
        
        # Simulate what ElegantRL does in training
        state, info = env.reset()
        print(f"   Reset state shape: {state.shape}")
        print(f"   Reset state type: {type(state)}")
        print(f"   Reset state range: [{np.min(state):.3f}, {np.max(state):.3f}]")
        
        # Test multiple steps to see reward variation
        print(f"\n🎯 Testing step-by-step reward collection:")
        rewards_collected = []
        
        for step in range(10):
            action = env.action_space.sample()  # Random action
            next_state, reward, terminated, truncated, step_info = env.step(action)
            rewards_collected.append(reward)
            
            print(f"   Step {step+1}: Action={action[0]:6.3f}, "
                  f"Reward={reward:8.6f}, Done={terminated or truncated}")
            
            if terminated or truncated:
                print("   Environment terminated, resetting...")
                next_state, info = env.reset()
        
        reward_variation = np.std(rewards_collected)
        print(f"\n📊 Reward collection analysis:")
        print(f"   Collected rewards: {rewards_collected}")
        print(f"   Reward std: {reward_variation:8.6f}")
        
        # Test reward scaling impact
        print(f"\n🔧 Testing reward scaling impact:")
        print(f"   Environment reward_scaling: {env.reward_scaling}")
        
        # Simulate what happens with different reward scaling values
        for scaling in [1e-4, 1e-3, 1e-2, 1e-1, 1.0]:
            # Create temporary env with different scaling
            temp_env = AsymmetricTradingEnv(
                df=test_data,
                stock_dim=1,
                hmax=100,
                initial_amount=100000,
                num_stock_shares=[0],
                buy_cost_pct=[0.001],
                sell_cost_pct=[0.001],
                reward_scaling=scaling,
                tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
                asymmetric_config=asymmetric_config,
                log_level="ERROR"
            )
            
            temp_state, temp_info = temp_env.reset()
            temp_action = temp_env.action_space.sample()
            _, temp_reward, _, _, _ = temp_env.step(temp_action)
            
            print(f"   Scaling {scaling:6.0e}: Reward={temp_reward:10.8f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in ElegantRL evaluator test: {e}")
        traceback.print_exc()
        return False

def test_get_rewards_and_steps_function():
    """Test the get_rewards_and_steps function that ElegantRL evaluator uses."""
    print("\n" + "="*60)
    print("🧪 TEST 3: get_rewards_and_steps Function")
    print("="*60)
    
    try:
        # Import the evaluator functions
        from elegantrl.train.evaluator import get_rewards_and_steps
        print("✅ Successfully imported get_rewards_and_steps")
        
        # Create test environment
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        
        test_data = create_minimal_test_data(num_days=15, num_stocks=1)
        
        asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            volatility_lookback=5
        )
        
        env = AsymmetricTradingEnv(
            df=test_data,
            stock_dim=1,
            hmax=100,
            initial_amount=100000,
            num_stock_shares=[0],
            buy_cost_pct=[0.001],
            sell_cost_pct=[0.001],
            reward_scaling=1e-4,
            tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
            asymmetric_config=asymmetric_config,
            log_level="ERROR"
        )
        
        # Create a dummy actor that returns different actions
        class DummyActor:
            def __init__(self, action_dim):
                self.action_dim = action_dim
                self.step_count = 0
            
            def __call__(self, state):
                """Return deterministic but varying actions."""
                self.step_count += 1
                # Create deterministic but varying actions
                action = np.array([0.1 * np.sin(self.step_count * 0.5)])
                return action
            
            def to(self, device):
                return self
            
            def eval(self):
                pass
        
        dummy_actor = DummyActor(env.action_space.shape[0])
        
        # Test get_rewards_and_steps with different evaluation episodes
        print(f"\n🔍 Testing get_rewards_and_steps function:")
        
        for eval_episodes in [1, 3, 5]:
            print(f"\n   Testing with {eval_episodes} evaluation episodes:")
            
            try:
                # Call the function that ElegantRL uses
                rewards, steps = get_rewards_and_steps(
                    env=env,
                    act=dummy_actor,
                    device=None,  # CPU
                    num_episode=eval_episodes,
                    max_step=50  # Limit steps per episode
                )
                
                print(f"     Returned rewards: {rewards}")
                print(f"     Returned steps: {steps}")
                print(f"     Reward mean: {np.mean(rewards):8.6f}")
                print(f"     Reward std: {np.std(rewards):8.6f}")
                
                # Calculate avgR as ElegantRL does
                if len(rewards) > 0:
                    avg_r = np.mean(rewards)
                    print(f"     avgR (as ElegantRL calculates): {avg_r:8.6f}")
                
            except Exception as e:
                print(f"     ❌ Error with {eval_episodes} episodes: {e}")
        
        # Test with random actor for comparison
        print(f"\n🎲 Testing with random actor:")
        
        class RandomActor:
            def __init__(self, action_dim):
                self.action_dim = action_dim
            
            def __call__(self, state):
                return np.random.uniform(-0.5, 0.5, size=(self.action_dim,))
            
            def to(self, device):
                return self
            
            def eval(self):
                pass
        
        random_actor = RandomActor(env.action_space.shape[0])
        
        try:
            rewards, steps = get_rewards_and_steps(
                env=env,
                act=random_actor,
                device=None,
                num_episode=5,
                max_step=50
            )
            
            print(f"   Random actor rewards: {rewards}")
            print(f"   Random actor avgR: {np.mean(rewards):8.6f}")
            print(f"   Random actor std: {np.std(rewards):8.6f}")
            
        except Exception as e:
            print(f"   ❌ Error with random actor: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in get_rewards_and_steps test: {e}")
        traceback.print_exc()
        return False

def test_training_setup_replication():
    """Replicate the exact training setup to identify the issue."""
    print("\n" + "="*60)
    print("🧪 TEST 4: Training Setup Replication")
    print("="*60)
    
    try:
        # Import training components as used in actual training
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        from elegantrl.agent import AgentSAC
        from elegantrl.run import train_and_evaluate
        
        print("✅ Training components imported successfully")
        
        # Replicate the data preparation from train_asymmetric.py
        test_data = create_minimal_test_data(num_days=30, num_stocks=1)
        
        # Use the same parameters as actual training
        stock_dim = 1
        initial_amount = 100000
        tech_indicators = ['sma_5', 'sma_10', 'ema_12', 'rsi_14', 'macd_12_26_9', 'adx_14']
        
        # Calculate state space as done in training
        base_state = 1 + stock_dim + stock_dim  # cash + prices + holdings
        tech_state = len(tech_indicators) * stock_dim
        asymmetric_state = stock_dim * 3  # volatility + momentum + asymmetry_score
        state_space = base_state + tech_state + asymmetric_state
        
        print(f"📐 State space calculation:")
        print(f"   Base state: {base_state}")
        print(f"   Tech state: {tech_state}")
        print(f"   Asymmetric state: {asymmetric_state}")
        print(f"   Total state space: {state_space}")
        
        # Create asymmetric config as in training
        asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            volatility_lookback=20
        )
        
        # Create environment exactly as in training
        env = AsymmetricTradingEnv(
            df=test_data,
            stock_dim=stock_dim,
            hmax=100,
            initial_amount=initial_amount,
            num_stock_shares=[0] * stock_dim,
            buy_cost_pct=[0.001] * stock_dim,
            sell_cost_pct=[0.001] * stock_dim,
            reward_scaling=1e-4,
            state_space=state_space,
            action_space=stock_dim,
            tech_indicator_list=tech_indicators,
            turbulence_threshold=1.0,
            make_plots=False,
            print_verbosity=10,
            mode="train",
            model_name="asymmetric_sac",
            asymmetric_config=asymmetric_config,
            log_level="ERROR"
        )
        
        print(f"✅ Environment created with exact training parameters")
        print(f"   Observation space: {env.observation_space.shape}")
        print(f"   Action space: {env.action_space.shape}")
        
        # Test environment state consistency
        print(f"\n🔍 Testing environment state consistency:")
        
        for reset_num in range(3):
            state, info = env.reset()
            print(f"   Reset {reset_num+1}: State shape={state.shape}, "
                  f"State mean={np.mean(state):.6f}, State std={np.std(state):.6f}")
            
            # Take a few steps and check reward variation
            episode_rewards = []
            for step in range(5):
                action = np.array([0.1 * (step + 1) / 5])  # Gradually increasing action
                next_state, reward, terminated, truncated, step_info = env.step(action)
                episode_rewards.append(reward)
                
                if terminated or truncated:
                    break
            
            print(f"     Episode rewards: {episode_rewards}")
            print(f"     Episode reward std: {np.std(episode_rewards):.8f}")
        
        # Test the exact env.get_gym_env_args call as used in training
        print(f"\n🔧 Testing gym environment arguments:")
        try:
            # This is how training script calls the environment
            env_args = {
                'env_name': 'AsymmetricTradingEnv',
                'state_dim': env.observation_space.shape[0],
                'action_dim': env.action_space.shape[0],
                'if_discrete': False
            }
            print(f"   Environment args: {env_args}")
            
        except Exception as e:
            print(f"   Error getting env args: {e}")
        
        # Manual evaluation simulation
        print(f"\n🎯 Manual evaluation simulation (replicating ElegantRL):")
        
        # Create a simple actor-like object
        class TestActor:
            def __init__(self):
                self.call_count = 0
            
            def __call__(self, state):
                self.call_count += 1
                # Return different actions to ensure variation
                return np.array([0.1 * np.sin(self.call_count * 0.3)])
        
        test_actor = TestActor()
        
        # Run evaluation episodes manually
        eval_rewards = []
        for episode in range(5):
            state, info = env.reset()
            episode_reward = 0
            step_count = 0
            
            done = False
            while not done and step_count < 20:
                action = test_actor(state)
                state, reward, terminated, truncated, step_info = env.step(action)
                episode_reward += reward
                step_count += 1
                done = terminated or truncated
            
            eval_rewards.append(episode_reward)
            print(f"   Evaluation episode {episode+1}: "
                  f"Total reward={episode_reward:.6f}, Steps={step_count}")
        
        print(f"\n📊 Manual evaluation results:")
        print(f"   Episode rewards: {eval_rewards}")
        print(f"   Mean (avgR): {np.mean(eval_rewards):.6f}")
        print(f"   Std: {np.std(eval_rewards):.6f}")
        
        # Check if this matches what ElegantRL would see
        variation_success = np.std(eval_rewards) > 1e-8
        print(f"\n{'✅ SUCCESS' if variation_success else '❌ ISSUE'}: "
              f"Manual evaluation shows {'varying' if variation_success else 'constant'} rewards")
        
        return variation_success
        
    except Exception as e:
        print(f"❌ Error in training setup replication: {e}")
        traceback.print_exc()
        return False

def main():
    """Main debug function."""
    print("🔍 ElegantRL Evaluator Constant avgR Debug Script")
    print("=" * 60)
    print("This script will test multiple aspects of the training setup")
    print("to identify why ElegantRL evaluator shows constant avgR values.")
    print("=" * 60)
    
    results = {}
    
    # Test 1: Individual environment rewards
    try:
        success1, episode_details = test_individual_environment_rewards()
        results['individual_env_test'] = success1
    except Exception as e:
        print(f"❌ Test 1 failed with exception: {e}")
        results['individual_env_test'] = False
    
    # Test 2: ElegantRL evaluator setup
    try:
        success2 = test_elegantrl_evaluator_setup()
        results['elegantrl_setup_test'] = success2
    except Exception as e:
        print(f"❌ Test 2 failed with exception: {e}")
        results['elegantrl_setup_test'] = False
    
    # Test 3: get_rewards_and_steps function
    try:
        success3 = test_get_rewards_and_steps_function()
        results['rewards_and_steps_test'] = success3
    except Exception as e:
        print(f"❌ Test 3 failed with exception: {e}")
        results['rewards_and_steps_test'] = False
    
    # Test 4: Training setup replication
    try:
        success4 = test_training_setup_replication()
        results['training_setup_test'] = success4
    except Exception as e:
        print(f"❌ Test 4 failed with exception: {e}")
        results['training_setup_test'] = False
    
    # Final analysis
    print("\n" + "="*60)
    print("🎯 FINAL ANALYSIS")
    print("="*60)
    
    print(f"\nTest Results:")
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    passing_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\nOverall: {passing_tests}/{total_tests} tests passed")
    
    if passing_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED!")
        print("   The environment produces varying rewards.")
        print("   The constant avgR issue may be:")
        print("   1. In the ElegantRL training loop itself")
        print("   2. In the way the environment is being called during training")
        print("   3. In the agent's action selection during evaluation")
        print("   4. In the reward scaling or normalization")
    elif results.get('individual_env_test', False):
        print("\n⚠️  PARTIAL SUCCESS")
        print("   Individual environment works but ElegantRL integration has issues.")
        print("   Focus on how ElegantRL calls the environment.")
    else:
        print("\n❌ CORE ISSUE IDENTIFIED")
        print("   The environment itself is not producing varying rewards.")
        print("   Check the reward calculation logic in AsymmetricTradingEnv.")
    
    print("\n💡 RECOMMENDATIONS:")
    if not results.get('individual_env_test', False):
        print("   1. Fix the reward calculation in AsymmetricTradingEnv.step()")
        print("   2. Ensure portfolio values change between steps")
        print("   3. Check that actions actually affect the environment state")
    elif not results.get('rewards_and_steps_test', False):
        print("   1. Check ElegantRL's get_rewards_and_steps function call")
        print("   2. Ensure the actor is producing different actions")
        print("   3. Verify environment reset/step sequence")
    else:
        print("   1. Add logging to the actual training loop")
        print("   2. Check if evaluation frequency is too low")
        print("   3. Verify that trained agent actions vary during evaluation")
    
    return passing_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)