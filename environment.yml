name: finrl-trading-agent
channels:
  - conda-forge
  - pytorch
  - defaults
dependencies:
  # Python
  - python=3.11
  
  # Core data science libraries
  - numpy>=1.24.0
  - pandas>=2.0.0
  - scipy>=1.10.0
  
  # Visualization
  - matplotlib>=3.7.0
  - seaborn>=0.12.0
  - plotly>=5.14.0
  
  # Machine Learning
  - scikit-learn>=1.3.0
  - pytorch>=2.0.0
  - torchvision
  - torchaudio
  
  # Development tools
  - jupyter>=1.0.0
  - ipykernel
  - pytest>=7.0.0
  - pytest-cov
  - black
  - flake8
  - mypy
  
  # System utilities
  - pip
  
  # Pip dependencies
  - pip:
    # Trading and financial data
    - alpaca-trade-api>=3.0.0
    - yfinance>=0.2.0
    - pandas-ta>=0.3.14b
    
    # Configuration and environment
    - python-dotenv>=1.0.0
    - pydantic>=2.0.0
    - pydantic-settings>=2.0.0
    
    # CLI and utilities
    - click>=8.0.0
    - loguru>=0.7.0
    - tqdm>=4.65.0
    
    # Hyperparameter optimization
    - optuna>=3.0.0
    - optuna-dashboard>=0.13.0
    
    # Reinforcement Learning
    - gym>=0.26.0
    - stable-baselines3>=2.0.0
    
    # FinRL and ElegantRL from GitHub
    #- git+https://github.com/AI4Finance-Foundation/FinRL.git
    #- git+https://github.com/AI4Finance-Foundation/ElegantRL.git
    
    # Additional utilities
    - requests>=2.31.0
    - beautifulsoup4>=4.12.0
    - lxml>=4.9.0
    
    # Performance monitoring
    - psutil>=5.9.0
    - memory-profiler>=0.60.0
    
    # Data validation
    - cerberus>=1.3.4
    - jsonschema>=4.17.0
    
    # Parallel processing
    - joblib>=1.3.0
    - multiprocess>=0.70.0
    
    # Time series analysis
    - statsmodels>=0.14.0
    - arch>=6.2.0
    
    # Documentation
    - sphinx>=7.0.0
    - sphinx-rtd-theme>=1.3.0
    
    # Testing utilities
    - pytest-mock>=3.11.0
    - pytest-asyncio>=0.21.0
    - factory-boy>=3.3.0