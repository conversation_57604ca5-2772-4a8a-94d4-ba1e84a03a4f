#!/usr/bin/env python3
"""
Debug why sell actions aren't generating positive rewards
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔍 Debugging sell action reward calculation...")

# Simple 3-day scenario: buy, hold, sell
test_data = []
prices = [100, 110, 120]  # 20% increase over 3 days

for i, price in enumerate(prices):
    test_data.append({
        'tic': 'AAPL',
        'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
        'close': price, 'open': price, 'high': price+1, 'low': price-1, 'volume': 1000000,
        'sma_5': price, 'sma_10': price, 'sma_20': price, 'rsi_14': 50.0,
        'macd_12_26_9': 0.0, 'ema_12': price, 'ema_26': price, 'cci_20': 0.0,
        'adx_14': 30.0, 'bbl_20_2.0': price-3, 'bbm_20_2.0': price,
        'bbu_20_2.0': price+3, 'obv': 1000000, 'turbulence': 0.1
    })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📊 Simple 3-day test: $100 → $110 → $120")

# Create environment with detailed logging
env = AsymmetricTradingEnv(
    df=df,
    stock_dim=1,
    hmax=1000,
    initial_amount=100000,
    num_stock_shares=[0],
    buy_cost_pct=[0.001],
    sell_cost_pct=[0.001],
    reward_scaling=1e-4,
    asymmetric_config=AsymmetricConfig(symbols=['AAPL']),
    log_level='INFO',  # Enable detailed logging
    tech_indicator_list=['sma_5', 'sma_10', 'sma_20', 'rsi_14', 'macd_12_26_9', 'ema_12', 'ema_26', 'cci_20', 'adx_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'obv', 'turbulence']
)

state, info = env.reset()
print(f"💰 Starting: ${env.asset_memory[0]:,.2f}")

# Day 1: Buy heavily
print(f"\n📅 Day 1: BUY at $100")
state, reward, terminated, truncated, info = env.step(np.array([0.9]))  # 90% buy signal
portfolio_before_buy = env.asset_memory[-2] if len(env.asset_memory) > 1 else env.asset_memory[0]
portfolio_after_buy = info.get('total_asset', 0)
print(f"💰 Before: ${portfolio_before_buy:,.2f} → After: ${portfolio_after_buy:,.2f}")
print(f"🎯 Reward: {reward:.8f}")
print(f"💸 Cash: ${info.get('cash', 0):,.2f}")
print(f"📊 Holdings: {info.get('holdings', [])}")

# Day 2: Hold (price goes up to $110)
print(f"\n📅 Day 2: HOLD at $110 (price increased)")
state, reward, terminated, truncated, info = env.step(np.array([0.0]))  # Hold
portfolio_before_hold = env.asset_memory[-2] if len(env.asset_memory) > 1 else 0
portfolio_after_hold = info.get('total_asset', 0)
print(f"💰 Before: ${portfolio_before_hold:,.2f} → After: ${portfolio_after_hold:,.2f}")
print(f"🎯 Reward: {reward:.8f}")
print(f"💸 Cash: ${info.get('cash', 0):,.2f}")
print(f"📊 Holdings: {info.get('holdings', [])}")

# Day 3: Sell at $120 (should be very profitable)
print(f"\n📅 Day 3: SELL at $120 (should be profitable!)")
state, reward, terminated, truncated, info = env.step(np.array([-1.0]))  # 100% sell signal
portfolio_before_sell = env.asset_memory[-2] if len(env.asset_memory) > 1 else 0
portfolio_after_sell = info.get('total_asset', 0)
print(f"💰 Before: ${portfolio_before_sell:,.2f} → After: ${portfolio_after_sell:,.2f}")
print(f"🎯 Reward: {reward:.8f}")
print(f"💸 Cash: ${info.get('cash', 0):,.2f}")
print(f"📊 Holdings: {info.get('holdings', [])}")

# Calculate what the reward should be
if len(env.asset_memory) >= 2:
    portfolio_change = env.asset_memory[-1] - env.asset_memory[-2]
    expected_return = portfolio_change / env.asset_memory[-2] if env.asset_memory[-2] > 0 else 0
    expected_reward = expected_return * env.reward_scaling
    print(f"\n🧮 CALCULATION CHECK:")
    print(f"Portfolio change: ${portfolio_change:,.2f}")
    print(f"Return rate: {expected_return:.8f}")
    print(f"Expected reward: {expected_reward:.8f}")
    print(f"Actual reward: {reward:.8f}")
    print(f"Match: {'✅' if abs(expected_reward - reward) < 1e-10 else '❌'}")

print(f"\n📈 FINAL RESULTS:")
final_return = ((env.asset_memory[-1] / env.asset_memory[0]) - 1) * 100
print(f"Total return: {final_return:+.2f}%")
print(f"Expected result: Should be profitable due to 20% price increase!")