#!/usr/bin/env python3
"""
Test training with determinism fix to verify avgR variation
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig
from models.sac_agent import SACAgent

print("🔍 Testing training with determinism fix...")

# Create realistic test data
test_data = []
symbols = ['AAPL', 'MSFT', 'GOOGL']

for i in range(50):  # 50 trading days
    for j, stock in enumerate(symbols):
        base_price = 100 + j*50  # Different base prices
        price = base_price + i*0.5 + np.random.normal(0, 1)  # Add some variation
        test_data.append({
            'tic': stock,
            'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': price, 'open': price*0.99, 'high': price*1.02, 'low': price*0.98, 
            'volume': 1000000,
            'sma_5': price, 'sma_10': price, 'sma_20': price, 'sma_50': price,
            'ema_12': price, 'ema_26': price, 'rsi_14': 50.0 + np.random.normal(0, 5),
            'macd_12_26_9': np.random.normal(0, 0.1), 'macds_12_26_9': np.random.normal(0, 0.1),
            'macdh_12_26_9': np.random.normal(0, 0.1), 'cci_20': np.random.normal(0, 20),
            'adx_14': 30.0, 'dmp_14': 25.0, 'dmn_14': 25.0,
            'bbl_20_2.0': price*0.95, 'bbm_20_2.0': price, 'bbu_20_2.0': price*1.05,
            'bbb_20_2.0': 0.1, 'bbp_20_2.0': 0.5, 'obv': 1000000, 'turbulence': 0.1,
            'price_range': 0.02, 'price_position': 0.5, 'returns_1d': 0.01,
            'returns_5d': 0.05, 'returns_20d': 0.2, 'volume_ma_20': 1000000,
            'volume_ratio': 1.0, 'volatility_20d': 0.02, 'vix_ma_5': 20.0,
            'vix_ma_20': 20.0, 'vix_percentile_252': 0.5, 'vix_change': 0.0,
            'vix_change_5d': 0.0, 'vix_regime_numeric': 1.0
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['date', 'tic']).reset_index(drop=True)
df['day'] = df['date'].factorize()[0]
df = df.set_index('day')

print(f"📊 Training data: {len(df)} rows, {len(df.index.unique())} days, {df['tic'].nunique()} stocks")

try:
    # Create environment
    env = AsymmetricTradingEnv(
        df=df,
        stock_dim=3,
        hmax=100,
        initial_amount=100000,
        num_stock_shares=[0, 0, 0],
        buy_cost_pct=[0.001, 0.001, 0.001],
        sell_cost_pct=[0.001, 0.001, 0.001],
        reward_scaling=1.0,  # Use higher scaling for meaningful rewards
        asymmetric_config=AsymmetricConfig(symbols=symbols),
        log_level='ERROR',
        tech_indicator_list=[
            'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 
            'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
            'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 
            'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 
            'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 
            'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 
            'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
        ]
    )
    
    print(f"✅ Environment created: max_step={env.max_step}")
    
    # Create SAC agent
    agent_config = {
        'learning_rate': 0.001,
        'gamma': 0.99,
        'tau': 0.005,
        'alpha': 0.2,
        'batch_size': 64,
        'buffer_size': 10000,
        'net_dims': [64, 64],
        'repeat_times': 1.0,
        'reward_scale': 1.0,
        'if_per': False,
        'if_off_policy': True,
        'checkpoint_dir': 'models/checkpoints'
    }
    
    sac_agent = SACAgent(
        state_dim=env.observation_space.shape[0],
        action_dim=env.action_space.shape[0],
        config=agent_config,
        asymmetric_config=AsymmetricConfig(symbols=symbols)
    )
    
    print(f"✅ SAC agent created")
    
    # Run short training to test avgR variation
    print(f"\n🏃 Running training with determinism fix...")
    print(f"Look for avgR variation in the ElegantRL output!")
    
    results = sac_agent.train(
        env=env,
        total_timesteps=3000,  # Short training for testing
        eval_env=None,
        eval_freq=1000,  # Evaluate frequently to see avgR
        save_freq=10000,
        model_dir='models/checkpoints'
    )
    
    print(f"\n✅ Training completed!")
    print(f"Results: {results}")
    
    # Check if we see avgR variation in the recorder
    try:
        recorder = np.load('models/checkpoints/recorder.npy')
        print(f"\n📊 Training Statistics from recorder:")
        print(f"  Shape: {recorder.shape}")
        if recorder.shape[0] > 1:
            print(f"  Multiple evaluation points recorded - good!")
            for i in range(min(recorder.shape[0], 5)):
                total_step = recorder[i, 0]
                avg_r = recorder[i, 1] 
                print(f"    Step {total_step:.0f}: avgR = {avg_r:.6f}")
            
            # Check variation
            avg_r_values = recorder[:, 1]
            std_avgR = np.std(avg_r_values)
            print(f"  avgR standard deviation across evaluations: {std_avgR:.8f}")
            
            if std_avgR > 1e-6:
                print(f"  ✅ SUCCESS: avgR shows variation across evaluations!")
            else:
                print(f"  ❌ avgR still shows little variation")
        else:
            print(f"  Only one evaluation recorded")
            
    except Exception as e:
        print(f"Could not read recorder: {e}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n📋 Expected outcome:")
print(f"If the fix works, you should see varying avgR values in the console output")
print(f"instead of constant -0.03 or similar values.")