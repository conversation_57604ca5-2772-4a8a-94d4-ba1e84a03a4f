#!/usr/bin/env python3
"""Debug script to check settings loading"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config.settings import Settings
from models.training import TrainingConfig
import os

print("=== Environment Variables ===")
print(f"SAC_TOTAL_TIMESTEPS from os.getenv(): {os.getenv('SAC_TOTAL_TIMESTEPS')}")

print("\n=== Settings Instance ===")
settings = Settings()
print(f"settings.sac.total_timesteps: {settings.sac.total_timesteps}")

print("\n=== TrainingConfig with settings ===")
best_params = {}  # Empty like in main.py when not using best params
training_config = TrainingConfig(
    total_timesteps=best_params.get('total_timesteps', settings.sac.total_timesteps),
    eval_freq=best_params.get('eval_freq', settings.sac.eval_freq),
    save_freq=best_params.get('save_freq', settings.sac.save_freq),
    log_interval=best_params.get('log_interval', settings.sac.log_interval)
)
print(f"training_config.total_timesteps: {training_config.total_timesteps}")

print("\n=== Direct TrainingConfig defaults ===")
default_config = TrainingConfig()
print(f"default TrainingConfig.total_timesteps: {default_config.total_timesteps}")