#!/usr/bin/env python3
"""
Debug why multiprocessing environments don't have evaluation noise.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
from pathlib import Path

print("🔍 DEBUGGING MULTIPROCESSING ENVIRONMENT ISSUE")
print("="*50)

# The core issue: ElegantRL creates NEW environments in worker processes
# These new environments won't have our evaluation noise settings

print("1. Understanding the problem:")
print("   - Main process: env.enable_evaluation_noise() ✅")
print("   - Worker process: creates NEW environment without noise ❌")
print("   - Solution: Pass noise settings in environment configuration")

print("\n2. Checking current env_args in SAC agent...")

# Check what env_args are being passed
try:
    from models.sac_agent import SACAgent
    from trading.asymmetric_env import AsymmetricTradingEnv
    from strategies.asymmetric_strategy import AsymmetricConfig
    from config.settings import settings
    
    print(f"   Checking settings.env.reward_scaling: {settings.env.reward_scaling}")
    
    # Create a dummy environment to see what args are generated
    import pandas as pd
    
    # Small test data
    test_data = []
    for i in range(5):
        test_data.append({
            'tic': 'TEST', 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
            'close': 100, 'open': 100, 'high': 101, 'low': 99, 'volume': 1000000,
            'day': i, 'sma_5': 100, 'turbulence': 0.1, 'returns_1d': 0.001
        })
    
    df = pd.DataFrame(test_data).set_index('day')
    
    env = AsymmetricTradingEnv(
        df=df, stock_dim=1, hmax=100, initial_amount=100000,
        num_stock_shares=[0], buy_cost_pct=[0.001], sell_cost_pct=[0.001],
        reward_scaling=1e-4, asymmetric_config=AsymmetricConfig(symbols=['TEST']),
        log_level='ERROR', tech_indicator_list=['sma_5', 'turbulence', 'returns_1d']
    )
    
    print(f"   Environment reward_scaling: {env.reward_scaling}")
    print(f"   Environment _evaluation_noise_scale (initial): {getattr(env, '_evaluation_noise_scale', 'missing')}")
    
    # Enable noise
    env.enable_evaluation_noise(0.05)
    print(f"   Environment _evaluation_noise_scale (after enable): {env._evaluation_noise_scale}")
    
    # Check what happens when we simulate environment creation from env_args
    # This is what ElegantRL does in worker processes
    env_args = {
        'df': env.df,
        'stock_dim': env.stock_dim, 
        'hmax': env.hmax,
        'initial_amount': env.initial_amount,
        'num_stock_shares': env.num_stock_shares,
        'buy_cost_pct': env.buy_cost_pct,
        'sell_cost_pct': env.sell_cost_pct,
        'reward_scaling': env.reward_scaling,
        'asymmetric_config': env.asymmetric_config,
        'log_level': env.log_level,
        'tech_indicator_list': env.tech_indicator_list
    }
    
    print(f"\n3. Simulating worker process environment creation...")
    
    # Create new environment like ElegantRL worker would
    new_env = AsymmetricTradingEnv(**env_args)
    print(f"   New environment _evaluation_noise_scale: {getattr(new_env, '_evaluation_noise_scale', 'missing')}")
    
    # This shows the problem: new environment doesn't have noise!
    
    print(f"\n4. SOLUTION: Add evaluation noise to environment arguments")
    
    # The fix is to include evaluation noise in env_args
    # We need to modify the SAC agent to include this
    
except Exception as e:
    print(f"   ❌ Error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n🎯 ROOT CAUSE IDENTIFIED:")
print(f"   ElegantRL creates fresh environments in worker processes")
print(f"   These environments start with _evaluation_noise_scale = 0.0")
print(f"   Our enable_evaluation_noise() call only affects main process environment")

print(f"\n🔧 SOLUTION NEEDED:")
print(f"   1. Add _evaluation_noise_scale to environment constructor")
print(f"   2. Include evaluation_noise_scale in env_args")
print(f"   3. Auto-enable noise if evaluation_noise_scale > 0")

print(f"\nThis explains why you still see avgR constant and stdR = 0.0")
print(f"The worker processes are creating clean environments without noise!")