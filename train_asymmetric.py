#!/usr/bin/env python3
"""
Asymmetric Trading Strategy Training Script

This script implements a complete training pipeline for the asymmetric trading strategy,
integrating with the existing FinRL-bot3 project structure and leveraging the existing
data processing functionality and technical indicators.
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import pickle
warnings.filterwarnings('ignore')

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import project modules
from config.settings import Settings
from utils.logging import setup_logging, get_logger
from data.fetcher import DataFetcher
from data.processor import DataProcessor
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig
from models.training import TrainingConfig, TrainingMetrics, EarlyStopping
from models.sac_agent import SACAgent


class AsymmetricTrainer:
    """Trainer for asymmetric trading strategy."""
    
    def __init__(self, config_file: Optional[str] = None, log_level: str = "INFO"):
        """Initialize the asymmetric trainer.
        
        Args:
            config_file: Optional configuration file path
            log_level: Logging level
        """
        # Store log level for passing to environment
        self.log_level = log_level
        
        # Load settings
        self.settings = Settings(config_file=config_file)
        
        # Setup logging
        setup_logging(
            level=log_level,
            log_file=self.settings.logging.file_path,
            worker_id="asymmetric_trainer",
            rotation=self.settings.logging.rotation,
            retention=self.settings.logging.retention,
            format_type=self.settings.logging.format_type or "detailed",
            enable_console=True,
            enable_file=True
        )
        
        self.logger = get_logger(self.__class__.__name__)
        
        # Initialize components using existing project structure
        self.fetcher = DataFetcher()
        self.processor = DataProcessor(
            fetcher=self.fetcher,
            settings_obj=self.settings,
            tech_indicator_list=[tech.lower() for tech in self.settings.data.tech_indicator_list],
            vix_features=self.settings.data.vix_features,
            include_vix=self.settings.data.include_vix
        )
        
        # Training configuration
        self.training_config = TrainingConfig(
            total_timesteps=100000,
            eval_freq=5000,
            save_freq=10000,
            early_stopping_patience=10,
            checkpoint_dir=Path("checkpoints/asymmetric"),
            log_interval=1000,
            eval_episodes=5,
            max_training_time=3600,  # 1 hour
            warmup_steps=1000,
            validation_split=0.2
        )
        
        # Asymmetric strategy configuration
        self.asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            momentum_threshold=0.02,
            mean_reversion_threshold=0.05,
            rsi_oversold=30,
            rsi_overbought=70,
            fast_ma_period=10,
            slow_ma_period=50,
            bb_period=20,
            bb_std=2.0,
            volatility_lookback=20,
            base_position_size=0.05,
            max_asymmetric_multiplier=2.0,
            min_asymmetric_multiplier=0.5,
            asymmetric_stop_loss=0.03,
            asymmetric_take_profit=0.06
        )
        
        self.logger.info("AsymmetricTrainer initialized successfully")
        self.logger.info(f"Using {len(self.settings.data.tech_indicator_list)} technical indicators: {self.settings.data.tech_indicator_list[:5]}...")
        self.logger.info(f"VIX integration enabled: {self.settings.data.include_vix}")
    
    def _split_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Split data into training and validation sets.
        
        Args:
            data: Combined data DataFrame
            
        Returns:
            Tuple of (training_data, validation_data)
        """
        # Sort by date to ensure proper temporal split
        data = data.sort_values(['date', 'tic']).reset_index(drop=True)
        
        # Get unique dates for splitting
        unique_dates = sorted(data['date'].unique())
        split_idx = int(len(unique_dates) * self.training_config.validation_split)
        train_end_date = unique_dates[-split_idx] if split_idx > 0 else unique_dates[-1]
        
        # Split data
        train_data = data[data['date'] < train_end_date].copy()
        val_data = data[data['date'] >= train_end_date].copy()
        
        self.logger.info(f"Training data: {len(train_data)} records, {len(train_data['date'].unique())} unique dates")
        self.logger.info(f"Validation data: {len(val_data)} records, {len(val_data['date'].unique())} unique dates")
        
        # Prepare data for FinRL environment
        train_df = self._prepare_finrl_data(train_data)
        val_df = self._prepare_finrl_data(val_data)
        
        return train_df, val_df
    
    def _prepare_finrl_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform data format for FinRL environment compatibility.
        
        This method follows the same data preparation logic as the main project
        to ensure consistency with the existing training pipeline.
        
        Args:
            df: Input DataFrame
            
        Returns:
            FinRL-compatible DataFrame
        """
        # Ensure date column is datetime and handle timezone-aware datetimes
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'], utc=True).dt.tz_localize(None)
        
        # Column mapping for FinRL compatibility (following main.py pattern)
        column_mapping = {
            'date': 'date',
            'symbol': 'tic',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume'
        }
        
        # Add all technical indicators from settings to mapping (convert to lowercase)
        # Fix: Check actual data columns instead of settings names since pandas_ta generates uppercase
        for col in df.columns:
            if col in self.settings.data.tech_indicator_list:
                column_mapping[col] = col.lower()
        
        # Apply column renaming
        df = df.rename(columns=column_mapping)
        
        # Sort by date and tic
        df = df.sort_values(['date', 'tic']).reset_index(drop=True)
        
        # Create a proper index for FinRL
        # FinRL uses integer index where each index represents a time step
        df['day'] = df['date'].factorize()[0]
        
        # Set the day as index for FinRL's df.loc[day, :] operation
        df = df.set_index('day')
        
        # Remove duplicate columns if any
        df = df.loc[:, ~df.columns.duplicated(keep='first')]
        
        return df
    
    def load_or_process_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Load processed data or process raw data if needed.
        
        This method leverages the existing project's data processing pipeline
        and integrates with the processed data structure.
        
        Returns:
            Tuple of (training_data, validation_data)
        """
        processed_file = Path(self.settings.data.processed_dir) / self.settings.data.processed_file_name
        
        if processed_file.exists():
            self.logger.info(f"Loading processed data from {processed_file}")
            try:
                data = pd.read_csv(processed_file)
                self.logger.info(f"Loaded {len(data)} records from processed file")
                
                # Validate that all expected technical indicators are present
                missing_indicators = self._validate_technical_indicators(data)
                if missing_indicators:
                    self.logger.warning(f"Missing technical indicators: {missing_indicators}")
                    self.logger.info("Reprocessing data to ensure all indicators are present...")
                    return self._process_raw_data()
                
                return self._split_data(data)
            except Exception as e:
                self.logger.warning(f"Failed to load processed data: {e}. Processing raw data...")
        
        # Process raw data
        self.logger.info("Processing raw data using existing project pipeline...")
        return self._process_raw_data()
    
    def _validate_technical_indicators(self, data: pd.DataFrame) -> list:
        """Validate that all expected technical indicators are present in the data.
        
        Args:
            data: DataFrame to validate
            
        Returns:
            List of missing indicator names
        """
        expected_indicators = [indicator.lower() for indicator in self.settings.data.tech_indicator_list]
        available_columns = [col.lower() for col in data.columns]
        missing_indicators = [indicator for indicator in expected_indicators if indicator not in available_columns]
        
        self.logger.info(f"Expected {len(expected_indicators)} technical indicators")
        self.logger.info(f"Found {len(expected_indicators) - len(missing_indicators)} indicators in data")
        
        return missing_indicators
    
    def _process_raw_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Process raw data using the existing project's data processing pipeline.
        
        Returns:
            Tuple of (training_data, validation_data)
        """
        symbols = self.settings.data.symbols
        start_date = self.settings.data.train_start_date
        end_date = self.settings.data.test_end_date
        
        all_stock_data = []
        failed_symbols = []
        
        # Fetch and process data for each symbol using existing pipeline
        self.logger.info(f"Processing data for {len(symbols)} symbols using existing pipeline...")
        for symbol in symbols:
            self.logger.info(f"Processing data for {symbol}")
            try:
                # Fetch raw data (will use cache if available)
                raw_data = self.fetcher.fetch_symbol_data(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    force_refresh=False
                )
                
                if not raw_data.empty:
                    # Create a fresh copy before processing
                    data_to_process = raw_data.copy()
                    
                    # Process with technical indicators using existing processor
                    processed_data = self.processor.process_stock_data(
                        data=data_to_process,
                        add_indicators=True,
                        normalize=False  # Normalization handled later if needed
                    )
                    all_stock_data.append(processed_data)
                    self.logger.info(f"Successfully processed {len(processed_data)} records for {symbol}")
                else:
                    self.logger.warning(f"No data available for {symbol}")
                    failed_symbols.append(symbol)
                    
            except Exception as e:
                self.logger.error(f"Failed to process data for {symbol}: {e}")
                failed_symbols.append(symbol)
                continue
        
        if failed_symbols:
            self.logger.warning(f"Failed to process data for symbols: {failed_symbols}")
        
        if not all_stock_data:
            raise ValueError("No data was successfully processed for any symbol")
        
        # Combine all data
        combined_data = pd.concat(all_stock_data, ignore_index=True)
        combined_data = combined_data.sort_values(['tic', 'date']).reset_index(drop=True)
        
        self.logger.info(f"Combined data: {len(combined_data)} records")
        self.logger.info(f"Data columns: {len(combined_data.columns)} total")
        
        # Log technical indicators summary
        tech_indicators_found = [col for col in combined_data.columns 
                               if any(indicator.lower() in col.lower() 
                                     for indicator in self.settings.data.tech_indicator_list)]
        self.logger.info(f"Technical indicators found: {len(tech_indicators_found)}")
        
        # Save processed data for future use
        processed_file = Path(self.settings.data.processed_dir) / self.settings.data.processed_file_name
        processed_file.parent.mkdir(parents=True, exist_ok=True)
        combined_data.to_csv(processed_file, index=False)
        self.logger.info(f"Saved processed data to {processed_file}")
        
        return self._split_data(combined_data)
    
    def prepare_training_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Prepare training and validation data with proper technical indicator handling.
        
        Returns:
            Tuple of (training_data, validation_data)
        """
        # Load or process data
        train_df, val_df = self.load_or_process_data()
        
        # Convert technical indicator column names from uppercase to lowercase
        # This ensures compatibility between the data and the environment expectations
        for col in self.settings.data.tech_indicator_list:
            if col in train_df.columns and col.lower() not in train_df.columns:
                train_df[col.lower()] = train_df[col]
            if col in val_df.columns and col.lower() not in val_df.columns:
                val_df[col.lower()] = val_df[col]
        
        # Apply NaN filling and type checking for technical indicators
        # Following the same pattern as main.py for consistency
        self.logger.info("Applying NaN fill and type check for technical indicators in training data")
        for col in self.settings.data.tech_indicator_list:
            col_lower = col.lower()
            if col_lower in train_df.columns:
                column_data_to_convert = train_df[col_lower]
                if isinstance(column_data_to_convert, pd.DataFrame):
                    self.logger.warning(f"Column '{col_lower}' is a DataFrame, likely due to duplicate names. Selecting first column for numeric conversion.")
                    original_nan_count = column_data_to_convert.isnull().sum().sum()
                    column_data_to_convert = column_data_to_convert.iloc[:, 0]
                else:
                    original_nan_count = column_data_to_convert.isnull().sum()
                
                train_df[col_lower] = pd.to_numeric(column_data_to_convert, errors='coerce')
                coercion_nan_count = train_df[col_lower].isnull().sum()
                
                if coercion_nan_count > original_nan_count:
                    self.logger.warning(f"Tech indicator '{col_lower}' in training data had values coerced to NaN by pd.to_numeric.")
                if train_df[col_lower].isnull().all() and original_nan_count < len(train_df[col_lower]):
                    self.logger.warning(f"Tech indicator '{col_lower}' in training data became all NaNs after pd.to_numeric.")
                train_df[col_lower] = train_df[col_lower].fillna(0)
            else:
                self.logger.error(f"CRITICAL: Technical indicator '{col}' (lowercase: '{col_lower}') from settings.data.tech_indicator_list NOT FOUND in training DataFrame. Adding as zeros.")
                train_df[col_lower] = 0.0
        
        self.logger.info("Applying NaN fill and type check for technical indicators in validation data")
        for col in self.settings.data.tech_indicator_list:
            col_lower = col.lower()
            if col_lower in val_df.columns:
                column_data_to_convert_val = val_df[col_lower]
                if isinstance(column_data_to_convert_val, pd.DataFrame):
                    self.logger.warning(f"Column '{col_lower}' in validation data is a DataFrame, likely due to duplicate names. Selecting first column for numeric conversion.")
                    original_nan_count = column_data_to_convert_val.isnull().sum().sum()
                    column_data_to_convert_val = column_data_to_convert_val.iloc[:, 0]
                else:
                    original_nan_count = column_data_to_convert_val.isnull().sum()
                
                val_df[col_lower] = pd.to_numeric(column_data_to_convert_val, errors='coerce')
                coercion_nan_count = val_df[col_lower].isnull().sum()
                
                if coercion_nan_count > original_nan_count:
                    self.logger.warning(f"Tech indicator '{col_lower}' in validation data had values coerced to NaN by pd.to_numeric.")
                if val_df[col_lower].isnull().all() and original_nan_count < len(val_df[col_lower]):
                    self.logger.warning(f"Tech indicator '{col_lower}' in validation data became all NaNs after pd.to_numeric.")
                val_df[col_lower] = val_df[col_lower].fillna(0)
            else:
                self.logger.error(f"CRITICAL: Technical indicator '{col}' (lowercase: '{col_lower}') from settings.data.tech_indicator_list NOT FOUND in validation DataFrame. Adding as zeros.")
                val_df[col_lower] = 0.0
        
        self.logger.info(f"Training data prepared: {len(train_df)} records")
        self.logger.info(f"Validation data prepared: {len(val_df)} records")
        
        return train_df, val_df
    
    def create_asymmetric_environment(self, data: pd.DataFrame, mode: str = "train") -> Optional[AsymmetricTradingEnv]:
        """Create asymmetric trading environment using project settings.
        
        Args:
            data: Market data DataFrame
            mode: Environment mode (train/validation/test)
            
        Returns:
            AsymmetricTradingEnv instance or None if creation failed
        """
        try:
            self.logger.info(f"Creating {mode} environment...")
            
            # Get environment parameters from settings
            stock_dim = len(self.settings.data.symbols)
            tech_indicators = [indicator.lower() for indicator in self.settings.data.tech_indicator_list]  # Convert to lowercase to match data
            
            # Add required columns for FinRL compatibility
            if 'date' not in data.columns and 'Date' in data.columns:
                data['date'] = data['Date']
            
            # Add lowercase OHLC columns if not present
            ohlc_mapping = {'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}
            for orig_col, lower_col in ohlc_mapping.items():
                if orig_col in data.columns and lower_col not in data.columns:
                    data[lower_col] = data[orig_col]
            
            # Calculate state space dimensions
            base_state = 1 + stock_dim + stock_dim  # cash + prices + holdings
            tech_state = len(tech_indicators) * stock_dim
            asymmetric_state = stock_dim * 3  # volatility + momentum + asymmetry_score
            state_space = base_state + tech_state + asymmetric_state
            
            # Create environment with asymmetric configuration
            env = AsymmetricTradingEnv(
                df=data,
                stock_dim=stock_dim,
                hmax=self.settings.environment_config.hmax,
                initial_amount=self.settings.environment_config.initial_amount,
                num_stock_shares=[0] * stock_dim,
                buy_cost_pct=[self.settings.environment_config.transaction_cost_pct] * stock_dim,
                sell_cost_pct=[self.settings.environment_config.transaction_cost_pct] * stock_dim,
                reward_scaling=self.settings.environment_config.reward_scaling,
                state_space=state_space,
                action_space=stock_dim,
                tech_indicator_list=tech_indicators,
                turbulence_threshold=getattr(self.settings.environment_config, 'turbulence_threshold', 1.0),
                make_plots=False,
                print_verbosity=10,
                mode=mode,
                model_name="asymmetric_sac",
                # Asymmetric parameters from config
                asymmetric_config=self.asymmetric_config,
                # Pass log level to environment
                log_level=self.log_level
            )
            
            self.logger.info(f"{mode.capitalize()} environment created successfully")
            self.logger.info(f"State space: {state_space}, Action space: {stock_dim}")
            self.logger.info(f"Technical indicators: {len(tech_indicators)}")
            
            return env
            
        except Exception as e:
            self.logger.exception(f"Error creating {mode} environment: {e}")
            return None
    
    def train(self) -> bool:
        """Execute the complete training process.
        
        Returns:
            True if training successful, False otherwise
        """
        try:
            self.logger.info("Starting asymmetric trading strategy training...")
            
            # Prepare training and validation data
            train_data, val_data = self.prepare_training_data()
            
            # Create training environment
            train_env = self.create_asymmetric_environment(train_data, mode="train")
            if train_env is None:
                self.logger.error("Failed to create training environment")
                return False
            
            # Create validation environment
            val_env = self.create_asymmetric_environment(val_data, mode="validation")
            if val_env is None:
                self.logger.error("Failed to create validation environment")
                return False
            
            # Initialize training components
            training_metrics = TrainingMetrics(
                episode=0,
                timestep=0,
                episode_return=0.0,
                episode_length=0
            )
            early_stopping = EarlyStopping(
                patience=self.training_config.early_stopping_patience,
                threshold=0.01
            )
            
            # Create checkpoint directory
            self.training_config.checkpoint_dir.mkdir(parents=True, exist_ok=True)
            
            # Start training process
            self.logger.info(f"Training for {self.training_config.total_timesteps} timesteps")
            
            # This is where the actual training would happen
            # For now, we'll simulate a successful training
            success = self._execute_training_loop(
                train_env=train_env,
                val_env=val_env,
                training_metrics=training_metrics,
                early_stopping=early_stopping
            )
            
            if success:
                self.logger.info("Training completed successfully!")
                return True
            else:
                self.logger.error("Training failed")
                return False
                
        except Exception as e:
            self.logger.exception(f"Error during training: {e}")
            return False
    
    def _execute_training_loop(
        self, 
        train_env: AsymmetricTradingEnv, 
        val_env: AsymmetricTradingEnv,
        training_metrics: TrainingMetrics,
        early_stopping: EarlyStopping
    ) -> bool:
        """Execute the main training loop.
        
        Args:
            train_env: Training environment
            val_env: Validation environment
            training_metrics: Training metrics tracker
            early_stopping: Early stopping handler
            
        Returns:
            True if training successful, False otherwise
        """
        try:
            self.logger.info("Executing training loop...")
            
            # 1. Initialize the SAC agent
            self.logger.info("Creating SAC agent...")
            self.agent = SACAgent(
                state_dim=train_env.state_space,
                action_dim=train_env.action_space.shape[0],
                config=self.settings.sac.dict()
            )
            
            # 2. Train the agent
            self.logger.info(f"Starting SAC training for {self.training_config.total_timesteps} timesteps")
            
            # Train the agent using the SAC implementation
            training_results = self.agent.train(
                env=train_env,
                total_timesteps=self.training_config.total_timesteps,
                model_dir=str(self.training_config.checkpoint_dir)
            )
            
            # 3. Save final model
            final_model_path = self.training_config.checkpoint_dir / "final_model.pkl"
            self.agent.save_model(str(final_model_path))
            self.logger.info(f"Final model saved to: {final_model_path}")
            
            # Store agent for evaluation
            self.trained_agent = self.agent
            
            return True
            
        except Exception as e:
            self.logger.exception(f"Error in training loop: {e}")
            return False
    
    def _evaluate_on_validation(self, val_env: AsymmetricTradingEnv) -> float:
        """Evaluate model on validation environment.
        
        Args:
            val_env: Validation environment
            
        Returns:
            Average validation reward
        """
        # Placeholder evaluation
        return np.random.normal(0.05, 0.02)
    
    def _save_checkpoint(self, path: Path, step: int) -> None:
        """Save training checkpoint.
        
        Args:
            path: Checkpoint file path
            step: Current training step
        """
        try:
            if hasattr(self, 'agent') and self.agent is not None:
                # Save the agent model
                self.agent.save_model(str(path))
                self.logger.info(f"Checkpoint saved at step {step} to {path}")
            else:
                # Create a placeholder checkpoint with training state
                checkpoint_data = {
                    'step': step,
                    'timestamp': datetime.now().isoformat(),
                    'training_config': self.training_config.__dict__,
                    'asymmetric_config': self.asymmetric_config.__dict__
                }
                with open(path, 'wb') as f:
                    pickle.dump(checkpoint_data, f)
                self.logger.info(f"Training state checkpoint saved at step {step} to {path}")
        except Exception as e:
            self.logger.error(f"Failed to save checkpoint at step {step}: {e}")
    
    def evaluate_model(self, model_path: Optional[str] = None) -> Dict[str, Any]:
        """Evaluate trained model.
        
        Args:
            model_path: Path to saved model (optional)
            
        Returns:
            Evaluation results dictionary
        """
        try:
            self.logger.info("Starting model evaluation...")
            
            # Prepare validation data
            _, val_data = self.prepare_training_data()
            
            # Create evaluation environment
            eval_env = self.create_asymmetric_environment(val_data, mode="test")
            if eval_env is None:
                return {}
            
            # Use trained agent if available, otherwise load from path
            agent = None
            if hasattr(self, 'trained_agent') and self.trained_agent is not None:
                agent = self.trained_agent
                self.logger.info("Using trained agent for evaluation")
            elif model_path and os.path.exists(model_path):
                # Load agent from saved model
                # Use the same state_dim that was used during training (401)
                agent = SACAgent(
                    state_dim=401,  # Fixed to match trained model
                    action_dim=eval_env.action_space.shape[0],
                    config=self.settings.sac.dict()
                )
                agent.load_model(model_path)
                self.logger.info(f"Loaded agent from {model_path}")
            
            # Run evaluation episodes
            results = []
            for episode in range(self.training_config.eval_episodes):
                self.logger.info(f"Running evaluation episode {episode + 1}/{self.training_config.eval_episodes}")
                
                state, info = eval_env.reset()
                episode_reward = 0
                done = False
                
                while not done:
                    if agent is not None:
                        # Use trained agent for actions
                        action = agent.predict(state, deterministic=True)
                    else:
                        # Fallback to random actions
                        action = eval_env.action_space.sample()
                    
                    state, reward, done, info = eval_env.step(action)
                    episode_reward += reward
                
                results.append(episode_reward)
                self.logger.info(f"Episode {episode + 1} reward: {episode_reward:.4f}")
            
            # Calculate evaluation metrics
            eval_metrics = {
                'mean_reward': np.mean(results),
                'std_reward': np.std(results),
                'min_reward': np.min(results),
                'max_reward': np.max(results),
                'episodes': len(results)
            }
            
            # Get asymmetric metrics if available
            if hasattr(eval_env, 'get_asymmetric_metrics'):
                asymmetric_metrics = eval_env.get_asymmetric_metrics()
                eval_metrics.update(asymmetric_metrics)
            
            self.logger.info(f"Evaluation completed. Mean reward: {eval_metrics['mean_reward']:.4f}")
            return eval_metrics
            
        except Exception as e:
            self.logger.exception(f"Error during evaluation: {e}")
            return {}


def main():
    """Main function to run asymmetric trading strategy training."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Train Asymmetric Trading Strategy")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--log-level", type=str, default="INFO", 
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    parser.add_argument("--eval-only", action="store_true", 
                       help="Only run evaluation")
    parser.add_argument("--model-path", type=str, 
                       help="Path to model for evaluation")
    
    args = parser.parse_args()
    
    try:
        # Initialize trainer with project settings
        trainer = AsymmetricTrainer(
            config_file=args.config,
            log_level=args.log_level
        )
        
        if args.eval_only:
            # Run evaluation only
            if not args.model_path:
                trainer.logger.error("--model-path required for evaluation")
                sys.exit(1)
            
            results = trainer.evaluate_model(args.model_path)
            trainer.logger.info(f"Evaluation results: {results}")
        else:
            # Run full training pipeline
            success = trainer.train()
            if success:
                trainer.logger.info("Training completed successfully!")
                
                # Run evaluation on trained model
                results = trainer.evaluate_model()
                trainer.logger.info(f"Final evaluation results: {results}")
            else:
                trainer.logger.error("Training failed!")
                sys.exit(1)
                 
    except Exception as e:
        # Create a basic logger for error handling if trainer initialization failed
        try:
            trainer.logger.error(f"Error in main: {e}")
        except (NameError, AttributeError):
            # Fallback if trainer wasn't initialized
            from loguru import logger
            logger.exception(f"Error in main: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()