#!/usr/bin/env python3
"""Check the actual processed data file"""

import pandas as pd
import os

print("🔍 Checking the actual processed data file")

filepath = 'data/processed/processed_data.csv'

if os.path.exists(filepath):
    print(f"✅ Found file: {filepath}")
    print(f"📏 File size: {os.path.getsize(filepath)//1024//1024}MB")
    
    # Load the data
    df = pd.read_csv(filepath)
    print(f"\n📊 RAW PROCESSED DATA:")
    print(f"  Shape: {df.shape}")
    print(f"  Columns: {list(df.columns)}")
    print(f"  Unique dates: {df['date'].nunique()}")
    print(f"  Unique symbols: {df['tic'].nunique()}")
    print(f"  Date range: {df['date'].min()} to {df['date'].max()}")
    print(f"  Symbols: {sorted(df['tic'].unique())}")
    
    # Apply the EXACT same transformation as main.py
    print(f"\n🔄 APPLYING MAIN.PY TRANSFORMATION:")
    
    # Step 1: Date filtering (like main.py)
    df['date'] = pd.to_datetime(df['date'])
    train_df = df[
        (df['date'] >= '2016-01-01') & 
        (df['date'] <= '2022-12-31')
    ].reset_index(drop=True)
    
    print(f"1️⃣ After date filtering (2016-2022):")
    print(f"    Shape: {train_df.shape}")
    print(f"    Unique dates: {train_df['date'].nunique()}")
    print(f"    Expected: ~1762 days × 10 stocks = ~17,620 records")
    
    # Step 2: Sort (like main.py prepare_finrl_data)
    train_df = train_df.sort_values(['date', 'tic']).reset_index(drop=True)
    print(f"2️⃣ After sorting: {train_df.shape}")
    
    # Step 3: Factorize (THE CRITICAL STEP)
    print(f"3️⃣ Before factorize: {train_df['date'].nunique()} unique dates")
    train_df['day'] = train_df['date'].factorize()[0]
    print(f"3️⃣ After factorize: {train_df['day'].nunique()} unique day values")
    print(f"3️⃣ Day range: {train_df['day'].min()} to {train_df['day'].max()}")
    
    # Step 4: Set index (like main.py)
    train_df_final = train_df.set_index('day')
    final_unique_days = len(train_df_final.index.unique())
    print(f"4️⃣ After set_index: {final_unique_days} unique index values")
    print(f"4️⃣ Index range: {train_df_final.index.min()} to {train_df_final.index.max()}")
    
    # THE CRITICAL TEST - this is what AsymmetricTradingEnv sees
    print(f"\n🎯 CRITICAL RESULT:")
    print(f"len(env.df.index.unique()) = {final_unique_days}")
    print(f"Episodes will terminate at day: {final_unique_days - 1}")
    
    if final_unique_days <= 125:
        print(f"❌ BUG FOUND! Only {final_unique_days} days instead of ~1762")
        print(f"This explains why episodes terminate at step 123!")
        
        # Debug the factorize step
        print(f"\n🔍 DEBUGGING FACTORIZE:")
        sample_dates = train_df['date'].drop_duplicates().sort_values()
        print(f"  First 10 dates: {list(sample_dates.head(10))}")
        print(f"  Last 10 dates: {list(sample_dates.tail(10))}")
        
        # Check if there are date gaps
        date_diff = sample_dates.diff().dropna()
        print(f"  Typical date gap: {date_diff.mode().iloc[0] if len(date_diff.mode()) > 0 else 'N/A'}")
        print(f"  Max date gap: {date_diff.max()}")
        
    else:
        print(f"✅ Data looks good with {final_unique_days} days")
        print(f"The issue must be elsewhere in the environment logic")
        
        # Test accessing specific days
        print(f"\n🧪 Testing data access:")
        try:
            day_123 = train_df_final.loc[123, :]
            print(f"✅ Can access day 123: {len(day_123) if hasattr(day_123, '__len__') else 'single record'}")
        except Exception as e:
            print(f"❌ Cannot access day 123: {e}")
            
        try:
            day_1761 = train_df_final.loc[1761, :]
            print(f"✅ Can access day 1761: {len(day_1761) if hasattr(day_1761, '__len__') else 'single record'}")
        except Exception as e:
            print(f"❌ Cannot access day 1761: {e}")

else:
    print(f"❌ File not found: {filepath}")