#!/usr/bin/env python3
"""
Debug NaN values in SAC training during hyperparameter tuning.
"""

import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
import torch
from trading.asymmetric_env import AsymmetricTradingEnv
from strategies.asymmetric_strategy import AsymmetricConfig

print("🔍 DEBUGGING NaN VALUES IN SAC TRAINING")
print("="*50)

print("1. Analyzing potential causes of NaN...")
print("   • Extreme state values")
print("   • Extreme rewards")
print("   • Learning rate too high")
print("   • Gradient explosion")
print("   • Invalid network inputs")

print("\n2. Creating test environment to check state/reward ranges...")

# Create test data similar to training
test_data = []
symbols = ['AAPL', 'MSFT', 'GOOGL']

for day in range(100):  # 100 days
    for symbol in symbols:
        # Simulate realistic but controlled price movements
        base_price = {'AAPL': 150, 'MSFT': 300, 'GOOGL': 100}[symbol]
        price = base_price * (1.01 ** day) * (1 + np.random.normal(0, 0.02))  # 1% growth + 2% noise
        
        test_data.append({
            'tic': symbol, 'date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=day),
            'close': price, 'open': price*0.999, 'high': price*1.005, 'low': price*0.995,
            'volume': 1000000, 'day': day,
            # Add realistic technical indicators
            'sma_5': price * (1 + np.random.normal(0, 0.01)),
            'sma_10': price * (1 + np.random.normal(0, 0.01)),
            'sma_20': price * (1 + np.random.normal(0, 0.01)),
            'sma_50': price * (1 + np.random.normal(0, 0.01)),
            'ema_12': price * (1 + np.random.normal(0, 0.01)),
            'ema_26': price * (1 + np.random.normal(0, 0.01)),
            'rsi_14': 50 + np.random.normal(0, 10),  # RSI between 30-70 typically
            'macd_12_26_9': np.random.normal(0, 2),
            'macds_12_26_9': np.random.normal(0, 2),
            'macdh_12_26_9': np.random.normal(0, 2),
            'cci_20': np.random.normal(0, 50),
            'adx_14': 20 + abs(np.random.normal(0, 10)),  # Always positive
            'dmp_14': 15 + abs(np.random.normal(0, 10)),
            'dmn_14': 15 + abs(np.random.normal(0, 10)),
            'bbl_20_2.0': price * 0.95,
            'bbm_20_2.0': price,
            'bbu_20_2.0': price * 1.05,
            'bbb_20_2.0': 0.1,
            'bbp_20_2.0': 0.5,
            'obv': 1000000 + np.random.normal(0, 100000),
            'turbulence': abs(np.random.normal(0.1, 0.05)),  # Always positive
            'price_range': 0.01 + abs(np.random.normal(0, 0.005)),
            'price_position': 0.5 + np.random.normal(0, 0.1),
            'returns_1d': np.random.normal(0.001, 0.02),
            'returns_5d': np.random.normal(0.005, 0.05),
            'returns_20d': np.random.normal(0.02, 0.1),
            'volume_ma_20': 1000000,
            'volume_ratio': 1 + np.random.normal(0, 0.2),
            'volatility_20d': 0.02 + abs(np.random.normal(0, 0.01)),
            'vix_ma_5': 20 + np.random.normal(0, 5),
            'vix_ma_20': 20 + np.random.normal(0, 5),
            'vix_percentile_252': 0.5 + np.random.normal(0, 0.2),
            'vix_change': np.random.normal(0, 2),
            'vix_change_5d': np.random.normal(0, 5),
            'vix_regime_numeric': 1 + np.random.normal(0, 0.5)
        })

df = pd.DataFrame(test_data)
df = df.sort_values(['day', 'tic']).reset_index(drop=True)
df = df.set_index('day')

print(f"   Created dataset: {len(df)} rows, {len(df['day'].unique())} days")

# Create environment
tech_indicators = [
    'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26',
    'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
    'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0',
    'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position',
    'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio',
    'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252',
    'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence'
]

try:
    env = AsymmetricTradingEnv(
        df=df, stock_dim=3, hmax=100, initial_amount=100000,
        num_stock_shares=[0, 0, 0], buy_cost_pct=[0.001, 0.001, 0.001],
        sell_cost_pct=[0.001, 0.001, 0.001], reward_scaling=1e-4,
        asymmetric_config=AsymmetricConfig(symbols=symbols),
        log_level='ERROR', tech_indicator_list=tech_indicators,
        evaluation_noise_scale=0.0  # Disable for clean debug
    )
    
    print(f"   Environment created: state_dim={env.observation_space.shape[0]}")
    
    # Test state generation
    print(f"\n3. Testing state generation for NaN/Inf values...")
    
    state, _ = env.reset()
    print(f"   Initial state shape: {state.shape}")
    print(f"   Initial state range: [{state.min():.6f}, {state.max():.6f}]")
    print(f"   Has NaN: {np.isnan(state).any()}")
    print(f"   Has Inf: {np.isinf(state).any()}")
    
    if np.isnan(state).any():
        nan_indices = np.where(np.isnan(state))[0]
        print(f"   ❌ NaN found at indices: {nan_indices[:10]}...")  # Show first 10
    
    if np.isinf(state).any():
        inf_indices = np.where(np.isinf(state))[0]
        print(f"   ❌ Inf found at indices: {inf_indices[:10]}...")
    
    # Test a few steps
    print(f"\n4. Testing step rewards for extreme values...")
    
    actions = [
        np.array([0.3, 0.3, 0.4]),  # Normal allocation
        np.array([1.0, 0.0, 0.0]),  # All in one stock
        np.array([0.0, 0.0, 0.0]),  # Hold everything
    ]
    
    for i, action in enumerate(actions):
        state, reward, done, truncated, info = env.step(action)
        
        print(f"   Action {i+1}: {action}")
        print(f"     Reward: {reward:.8f}")
        print(f"     Portfolio: ${info.get('total_asset', 0):.2f}")
        print(f"     State range: [{state.min():.6f}, {state.max():.6f}]")
        print(f"     State has NaN: {np.isnan(state).any()}")
        print(f"     State has Inf: {np.isinf(state).any()}")
        
        if np.isnan(reward) or np.isinf(reward):
            print(f"     ❌ INVALID REWARD: {reward}")
        
        if done or truncated:
            break

except Exception as e:
    print(f"   ❌ Environment error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n🎯 LIKELY CAUSES OF NaN IN SAC:")
print(f"1. **State Normalization**: Large state values → network saturation")
print(f"2. **Reward Scaling**: Extreme rewards → gradient explosion")
print(f"3. **Learning Rate**: Too high → parameter divergence")
print(f"4. **Network Architecture**: Insufficient capacity for state space")
print(f"5. **Data Quality**: NaN/Inf in input data")

print(f"\n💡 SOLUTIONS:")
print(f"1. **State Normalization**: Add proper state scaling/clipping")
print(f"2. **Gradient Clipping**: Add max_grad_norm to prevent explosion")
print(f"3. **Learning Rate**: Use lower rates during tuning")
print(f"4. **Reward Clipping**: Cap rewards at reasonable values")
print(f"5. **Network Regularization**: Add dropout/batch norm")

print(f"\n🔧 IMMEDIATE FIXES TO IMPLEMENT:")
print(f"• Add state value clipping in environment")
print(f"• Add gradient norm clipping in SAC config")
print(f"• Use conservative learning rates for tuning")
print(f"• Add NaN checking in environment step")